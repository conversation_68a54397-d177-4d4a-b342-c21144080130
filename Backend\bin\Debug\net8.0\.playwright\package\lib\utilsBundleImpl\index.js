"use strict";var Yh=Object.create;var Ur=Object.defineProperty;var Kh=Object.getOwnPropertyDescriptor;var Zh=Object.getOwnPropertyNames;var Xh=Object.getPrototypeOf,Jh=Object.prototype.hasOwnProperty;var S=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Qh=(t,e)=>{for(var i in e)Ur(t,i,{get:e[i],enumerable:!0})},$o=(t,e,i,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Zh(e))!Jh.call(t,n)&&n!==i&&Ur(t,n,{get:()=>e[n],enumerable:!(r=Kh(e,n))||r.enumerable});return t};var He=(t,e,i)=>(i=t!=null?Yh(Xh(t)):{},$o(e||!t||!t.__esModule?Ur(i,"default",{value:t,enumerable:!0}):i,t)),ep=t=>$o(Ur({},"__esModule",{value:!0}),t);var Yo=S((a_,Wo)=>{var zo={};Wo.exports=zo;var Go={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(Go).forEach(function(t){var e=Go[t],i=zo[t]=[];i.open="\x1B["+e[0]+"m",i.close="\x1B["+e[1]+"m"})});var Zo=S((l_,Ko)=>{"use strict";Ko.exports=function(t,e){e=e||process.argv;var i=e.indexOf("--"),r=/^-{1,2}/.test(t)?"":"--",n=e.indexOf(r+t);return n!==-1&&(i===-1?!0:n<i)}});var Jo=S((c_,Xo)=>{"use strict";var tp=require("os"),Et=Zo(),tt=process.env,Ai=void 0;Et("no-color")||Et("no-colors")||Et("color=false")?Ai=!1:(Et("color")||Et("colors")||Et("color=true")||Et("color=always"))&&(Ai=!0);"FORCE_COLOR"in tt&&(Ai=tt.FORCE_COLOR.length===0||parseInt(tt.FORCE_COLOR,10)!==0);function ip(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function rp(t){if(Ai===!1)return 0;if(Et("color=16m")||Et("color=full")||Et("color=truecolor"))return 3;if(Et("color=256"))return 2;if(t&&!t.isTTY&&Ai!==!0)return 0;var e=Ai?1:0;if(process.platform==="win32"){var i=tp.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in tt)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(n){return n in tt})||tt.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in tt)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(tt.TEAMCITY_VERSION)?1:0;if("TERM_PROGRAM"in tt){var r=parseInt((tt.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(tt.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(tt.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(tt.TERM)||"COLORTERM"in tt?1:(tt.TERM==="dumb",e)}function Pn(t){var e=rp(t);return ip(e)}Xo.exports={supportsColor:Pn,stdout:Pn(process.stdout),stderr:Pn(process.stderr)}});var ea=S((u_,Qo)=>{Qo.exports=function(e,i){var r="";e=e||"Run the trap, drop the bass",e=e.split("");var n={a:["@","\u0104","\u023A","\u0245","\u0394","\u039B","\u0414"],b:["\xDF","\u0181","\u0243","\u026E","\u03B2","\u0E3F"],c:["\xA9","\u023B","\u03FE"],d:["\xD0","\u018A","\u0500","\u0501","\u0502","\u0503"],e:["\xCB","\u0115","\u018E","\u0258","\u03A3","\u03BE","\u04BC","\u0A6C"],f:["\u04FA"],g:["\u0262"],h:["\u0126","\u0195","\u04A2","\u04BA","\u04C7","\u050A"],i:["\u0F0F"],j:["\u0134"],k:["\u0138","\u04A0","\u04C3","\u051E"],l:["\u0139"],m:["\u028D","\u04CD","\u04CE","\u0520","\u0521","\u0D69"],n:["\xD1","\u014B","\u019D","\u0376","\u03A0","\u048A"],o:["\xD8","\xF5","\xF8","\u01FE","\u0298","\u047A","\u05DD","\u06DD","\u0E4F"],p:["\u01F7","\u048E"],q:["\u09CD"],r:["\xAE","\u01A6","\u0210","\u024C","\u0280","\u042F"],s:["\xA7","\u03DE","\u03DF","\u03E8"],t:["\u0141","\u0166","\u0373"],u:["\u01B1","\u054D"],v:["\u05D8"],w:["\u0428","\u0460","\u047C","\u0D70"],x:["\u04B2","\u04FE","\u04FC","\u04FD"],y:["\xA5","\u04B0","\u04CB"],z:["\u01B5","\u0240"]};return e.forEach(function(s){s=s.toLowerCase();var o=n[s]||[" "],f=Math.floor(Math.random()*o.length);typeof n[s]!="undefined"?r+=n[s][f]:r+=s}),r}});var ia=S((f_,ta)=>{ta.exports=function(e,i){e=e||"   he is here   ";var r={up:["\u030D","\u030E","\u0304","\u0305","\u033F","\u0311","\u0306","\u0310","\u0352","\u0357","\u0351","\u0307","\u0308","\u030A","\u0342","\u0313","\u0308","\u034A","\u034B","\u034C","\u0303","\u0302","\u030C","\u0350","\u0300","\u0301","\u030B","\u030F","\u0312","\u0313","\u0314","\u033D","\u0309","\u0363","\u0364","\u0365","\u0366","\u0367","\u0368","\u0369","\u036A","\u036B","\u036C","\u036D","\u036E","\u036F","\u033E","\u035B","\u0346","\u031A"],down:["\u0316","\u0317","\u0318","\u0319","\u031C","\u031D","\u031E","\u031F","\u0320","\u0324","\u0325","\u0326","\u0329","\u032A","\u032B","\u032C","\u032D","\u032E","\u032F","\u0330","\u0331","\u0332","\u0333","\u0339","\u033A","\u033B","\u033C","\u0345","\u0347","\u0348","\u0349","\u034D","\u034E","\u0353","\u0354","\u0355","\u0356","\u0359","\u035A","\u0323"],mid:["\u0315","\u031B","\u0300","\u0301","\u0358","\u0321","\u0322","\u0327","\u0328","\u0334","\u0335","\u0336","\u035C","\u035D","\u035E","\u035F","\u0360","\u0362","\u0338","\u0337","\u0361"," \u0489"]},n=[].concat(r.up,r.down,r.mid);function s(u){var h=Math.floor(Math.random()*u);return h}function o(u){var h=!1;return n.filter(function(l){h=l===u}),h}function f(u,h){var l="",d,m;h=h||{},h.up=typeof h.up!="undefined"?h.up:!0,h.mid=typeof h.mid!="undefined"?h.mid:!0,h.down=typeof h.down!="undefined"?h.down:!0,h.size=typeof h.size!="undefined"?h.size:"maxi",u=u.split("");for(m in u)if(!o(m)){switch(l=l+u[m],d={up:0,down:0,mid:0},h.size){case"mini":d.up=s(8),d.mid=s(2),d.down=s(8);break;case"maxi":d.up=s(16)+3,d.mid=s(4)+1,d.down=s(64)+3;break;default:d.up=s(8)+1,d.mid=s(6)/2,d.down=s(8)+1;break}var _=["up","mid","down"];for(var v in _)for(var x=_[v],b=0;b<=d[x];b++)h[x]&&(l=l+r[x][s(r[x].length)])}return l}return f(e,i)}});var na=S((h_,ra)=>{ra.exports=function(t){return function(e,i,r){if(e===" ")return e;switch(i%3){case 0:return t.red(e);case 1:return t.white(e);case 2:return t.blue(e)}}}});var oa=S((p_,sa)=>{sa.exports=function(t){return function(e,i,r){return i%2===0?e:t.inverse(e)}}});var la=S((d_,aa)=>{aa.exports=function(t){var e=["red","yellow","green","blue","magenta"];return function(i,r,n){return i===" "?i:t[e[r++%e.length]](i)}}});var ua=S((m_,ca)=>{ca.exports=function(t){var e=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(i,r,n){return i===" "?i:t[e[Math.round(Math.random()*(e.length-2))]](i)}}});var ga=S((v_,ma)=>{var ve={};ma.exports=ve;ve.themes={};var np=require("util"),fi=ve.styles=Yo(),ha=Object.defineProperties,sp=new RegExp(/[\r\n]+/g);ve.supportsColor=Jo().supportsColor;typeof ve.enabled=="undefined"&&(ve.enabled=ve.supportsColor()!==!1);ve.enable=function(){ve.enabled=!0};ve.disable=function(){ve.enabled=!1};ve.stripColors=ve.strip=function(t){return(""+t).replace(/\x1B\[\d+m/g,"")};var g_=ve.stylize=function(e,i){if(!ve.enabled)return e+"";var r=fi[i];return!r&&i in ve?ve[i](e):r.open+e+r.close},op=/[|\\{}()[\]^$+*?.]/g,ap=function(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(op,"\\$&")};function pa(t){var e=function i(){return cp.apply(i,arguments)};return e._styles=t,e.__proto__=lp,e}var da=function(){var t={};return fi.grey=fi.gray,Object.keys(fi).forEach(function(e){fi[e].closeRe=new RegExp(ap(fi[e].close),"g"),t[e]={get:function(){return pa(this._styles.concat(e))}}}),t}(),lp=ha(function(){},da);function cp(){var t=Array.prototype.slice.call(arguments),e=t.map(function(o){return o!=null&&o.constructor===String?o:np.inspect(o)}).join(" ");if(!ve.enabled||!e)return e;for(var i=e.indexOf(`
`)!=-1,r=this._styles,n=r.length;n--;){var s=fi[r[n]];e=s.open+e.replace(s.closeRe,s.open)+s.close,i&&(e=e.replace(sp,function(o){return s.close+o+s.open}))}return e}ve.setTheme=function(t){if(typeof t=="string"){console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");return}for(var e in t)(function(i){ve[i]=function(r){if(typeof t[i]=="object"){var n=r;for(var s in t[i])n=ve[t[i][s]](n);return n}return ve[t[i]](r)}})(e)};function up(){var t={};return Object.keys(da).forEach(function(e){t[e]={get:function(){return pa([e])}}}),t}var fp=function(e,i){var r=i.split("");return r=r.map(e),r.join("")};ve.trap=ea();ve.zalgo=ia();ve.maps={};ve.maps.america=na()(ve);ve.maps.zebra=oa()(ve);ve.maps.rainbow=la()(ve);ve.maps.random=ua()(ve);for(fa in ve.maps)(function(t){ve[t]=function(e){return fp(ve.maps[t],e)}})(fa);var fa;ha(ve,up())});var _a=S((__,va)=>{var hp=ga();va.exports=hp});var ya=S((x_,xa)=>{var Bi=1e3,Ri=Bi*60,Pi=Ri*60,hi=Pi*24,pp=hi*7,dp=hi*365.25;xa.exports=function(t,e){e=e||{};var i=typeof t;if(i==="string"&&t.length>0)return mp(t);if(i==="number"&&isFinite(t))return e.long?vp(t):gp(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function mp(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var i=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return i*dp;case"weeks":case"week":case"w":return i*pp;case"days":case"day":case"d":return i*hi;case"hours":case"hour":case"hrs":case"hr":case"h":return i*Pi;case"minutes":case"minute":case"mins":case"min":case"m":return i*Ri;case"seconds":case"second":case"secs":case"sec":case"s":return i*Bi;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}function gp(t){var e=Math.abs(t);return e>=hi?Math.round(t/hi)+"d":e>=Pi?Math.round(t/Pi)+"h":e>=Ri?Math.round(t/Ri)+"m":e>=Bi?Math.round(t/Bi)+"s":t+"ms"}function vp(t){var e=Math.abs(t);return e>=hi?jr(t,e,hi,"day"):e>=Pi?jr(t,e,Pi,"hour"):e>=Ri?jr(t,e,Ri,"minute"):e>=Bi?jr(t,e,Bi,"second"):t+" ms"}function jr(t,e,i,r){var n=e>=i*1.5;return Math.round(t/i)+" "+r+(n?"s":"")}});var Ln=S((y_,ba)=>{function _p(t){i.debug=i,i.default=i,i.coerce=u,i.disable=s,i.enable=n,i.enabled=o,i.humanize=ya(),i.destroy=h,Object.keys(t).forEach(l=>{i[l]=t[l]}),i.names=[],i.skips=[],i.formatters={};function e(l){let d=0;for(let m=0;m<l.length;m++)d=(d<<5)-d+l.charCodeAt(m),d|=0;return i.colors[Math.abs(d)%i.colors.length]}i.selectColor=e;function i(l){let d,m=null,_,v;function x(...b){if(!x.enabled)return;let T=x,B=Number(new Date),E=B-(d||B);T.diff=E,T.prev=d,T.curr=B,d=B,b[0]=i.coerce(b[0]),typeof b[0]!="string"&&b.unshift("%O");let R=0;b[0]=b[0].replace(/%([a-zA-Z%])/g,(X,A)=>{if(X==="%%")return"%";R++;let z=i.formatters[A];if(typeof z=="function"){let C=b[R];X=z.call(T,C),b.splice(R,1),R--}return X}),i.formatArgs.call(T,b),(T.log||i.log).apply(T,b)}return x.namespace=l,x.useColors=i.useColors(),x.color=i.selectColor(l),x.extend=r,x.destroy=i.destroy,Object.defineProperty(x,"enabled",{enumerable:!0,configurable:!1,get:()=>m!==null?m:(_!==i.namespaces&&(_=i.namespaces,v=i.enabled(l)),v),set:b=>{m=b}}),typeof i.init=="function"&&i.init(x),x}function r(l,d){let m=i(this.namespace+(typeof d=="undefined"?":":d)+l);return m.log=this.log,m}function n(l){i.save(l),i.namespaces=l,i.names=[],i.skips=[];let d,m=(typeof l=="string"?l:"").split(/[\s,]+/),_=m.length;for(d=0;d<_;d++)m[d]&&(l=m[d].replace(/\*/g,".*?"),l[0]==="-"?i.skips.push(new RegExp("^"+l.slice(1)+"$")):i.names.push(new RegExp("^"+l+"$")))}function s(){let l=[...i.names.map(f),...i.skips.map(f).map(d=>"-"+d)].join(",");return i.enable(""),l}function o(l){if(l[l.length-1]==="*")return!0;let d,m;for(d=0,m=i.skips.length;d<m;d++)if(i.skips[d].test(l))return!1;for(d=0,m=i.names.length;d<m;d++)if(i.names[d].test(l))return!0;return!1}function f(l){return l.toString().substring(2,l.toString().length-2).replace(/\.\*\?$/,"*")}function u(l){return l instanceof Error?l.stack||l.message:l}function h(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return i.enable(i.load()),i}ba.exports=_p});var wa=S((ht,qr)=>{ht.formatArgs=yp;ht.save=bp;ht.load=wp;ht.useColors=xp;ht.storage=Sp();ht.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();ht.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function xp(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function yp(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+qr.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let i=0,r=0;t[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(i++,n==="%c"&&(r=i))}),t.splice(r,0,e)}ht.log=console.debug||console.log||(()=>{});function bp(t){try{t?ht.storage.setItem("debug",t):ht.storage.removeItem("debug")}catch{}}function wp(){let t;try{t=ht.storage.getItem("debug")}catch{}return!t&&typeof process!="undefined"&&"env"in process&&(t=process.env.DEBUG),t}function Sp(){try{return localStorage}catch{}}qr.exports=Ln()(ht);var{formatters:Ep}=qr.exports;Ep.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var Ea=S((b_,Sa)=>{"use strict";Sa.exports=(t,e)=>{e=e||process.argv;let i=t.startsWith("-")?"":t.length===1?"-":"--",r=e.indexOf(i+t),n=e.indexOf("--");return r!==-1&&(n===-1?!0:r<n)}});var Ca=S((w_,ka)=>{"use strict";var kp=require("os"),kt=Ea(),Ze=process.env,Li;kt("no-color")||kt("no-colors")||kt("color=false")?Li=!1:(kt("color")||kt("colors")||kt("color=true")||kt("color=always"))&&(Li=!0);"FORCE_COLOR"in Ze&&(Li=Ze.FORCE_COLOR.length===0||parseInt(Ze.FORCE_COLOR,10)!==0);function Cp(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function Op(t){if(Li===!1)return 0;if(kt("color=16m")||kt("color=full")||kt("color=truecolor"))return 3;if(kt("color=256"))return 2;if(t&&!t.isTTY&&Li!==!0)return 0;let e=Li?1:0;if(process.platform==="win32"){let i=kp.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in Ze)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(i=>i in Ze)||Ze.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in Ze)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(Ze.TEAMCITY_VERSION)?1:0;if(Ze.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in Ze){let i=parseInt((Ze.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(Ze.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(Ze.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(Ze.TERM)||"COLORTERM"in Ze?1:(Ze.TERM==="dumb",e)}function Nn(t){let e=Op(t);return Cp(e)}ka.exports={supportsColor:Nn,stdout:Nn(process.stdout),stderr:Nn(process.stderr)}});var Ta=S((We,Vr)=>{var Tp=require("tty"),Hr=require("util");We.init=Np;We.log=Rp;We.formatArgs=Ap;We.save=Pp;We.load=Lp;We.useColors=Ip;We.destroy=Hr.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");We.colors=[6,2,3,4,5,1];try{let t=Ca();t&&(t.stderr||t).level>=2&&(We.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}We.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let i=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),t[i]=r,t},{});function Ip(){return"colors"in We.inspectOpts?!!We.inspectOpts.colors:Tp.isatty(process.stderr.fd)}function Ap(t){let{namespace:e,useColors:i}=this;if(i){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),s=`  ${n};1m${e} \x1B[0m`;t[0]=s+t[0].split(`
`).join(`
`+s),t.push(n+"m+"+Vr.exports.humanize(this.diff)+"\x1B[0m")}else t[0]=Bp()+e+" "+t[0]}function Bp(){return We.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Rp(...t){return process.stderr.write(Hr.format(...t)+`
`)}function Pp(t){t?process.env.DEBUG=t:delete process.env.DEBUG}function Lp(){return process.env.DEBUG}function Np(t){t.inspectOpts={};let e=Object.keys(We.inspectOpts);for(let i=0;i<e.length;i++)t.inspectOpts[e[i]]=We.inspectOpts[e[i]]}Vr.exports=Ln()(We);var{formatters:Oa}=Vr.exports;Oa.o=function(t){return this.inspectOpts.colors=this.useColors,Hr.inspect(t,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};Oa.O=function(t){return this.inspectOpts.colors=this.useColors,Hr.inspect(t,this.inspectOpts)}});var Ni=S((S_,Fn)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?Fn.exports=wa():Fn.exports=Ta()});var Aa=S(Ia=>{"use strict";var Fp=require("url").parse,Mp={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},Dp=String.prototype.endsWith||function(t){return t.length<=this.length&&this.indexOf(t,this.length-t.length)!==-1};function Up(t){var e=typeof t=="string"?Fp(t):t||{},i=e.protocol,r=e.host,n=e.port;if(typeof r!="string"||!r||typeof i!="string"||(i=i.split(":",1)[0],r=r.replace(/:\d*$/,""),n=parseInt(n)||Mp[i]||0,!jp(r,n)))return"";var s=Fi("npm_config_"+i+"_proxy")||Fi(i+"_proxy")||Fi("npm_config_proxy")||Fi("all_proxy");return s&&s.indexOf("://")===-1&&(s=i+"://"+s),s}function jp(t,e){var i=(Fi("npm_config_no_proxy")||Fi("no_proxy")).toLowerCase();return i?i==="*"?!1:i.split(/[,\s]/).every(function(r){if(!r)return!0;var n=r.match(/^(.+):(\d+)$/),s=n?n[1]:r,o=n?parseInt(n[2]):0;return o&&o!==e?!0:/^[.*]/.test(s)?(s.charAt(0)==="*"&&(s=s.slice(1)),!Dp.call(t,s)):t!==s}):!0}function Fi(t){return process.env[t.toLowerCase()]||process.env[t.toUpperCase()]||""}Ia.getProxyForUrl=Up});var Ba=S(Mn=>{"use strict";Object.defineProperty(Mn,"__esModule",{value:!0});function qp(t){return function(e,i){return new Promise((r,n)=>{t.call(this,e,i,(s,o)=>{s?n(s):r(o)})})}}Mn.default=qp});var jn=S((Un,Pa)=>{"use strict";var Ra=Un&&Un.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Hp=require("events"),Vp=Ra(Ni()),$p=Ra(Ba()),sr=Vp.default("agent-base");function Gp(t){return!!t&&typeof t.addRequest=="function"}function Dn(){let{stack:t}=new Error;return typeof t!="string"?!1:t.split(`
`).some(e=>e.indexOf("(https.js:")!==-1||e.indexOf("node:https:")!==-1)}function $r(t,e){return new $r.Agent(t,e)}(function(t){class e extends Hp.EventEmitter{constructor(r,n){super();let s=n;typeof r=="function"?this.callback=r:r&&(s=r),this.timeout=null,s&&typeof s.timeout=="number"&&(this.timeout=s.timeout),this.maxFreeSockets=1,this.maxSockets=1,this.maxTotalSockets=1/0,this.sockets={},this.freeSockets={},this.requests={},this.options={}}get defaultPort(){return typeof this.explicitDefaultPort=="number"?this.explicitDefaultPort:Dn()?443:80}set defaultPort(r){this.explicitDefaultPort=r}get protocol(){return typeof this.explicitProtocol=="string"?this.explicitProtocol:Dn()?"https:":"http:"}set protocol(r){this.explicitProtocol=r}callback(r,n,s){throw new Error('"agent-base" has no default implementation, you must subclass and override `callback()`')}addRequest(r,n){let s=Object.assign({},n);typeof s.secureEndpoint!="boolean"&&(s.secureEndpoint=Dn()),s.host==null&&(s.host="localhost"),s.port==null&&(s.port=s.secureEndpoint?443:80),s.protocol==null&&(s.protocol=s.secureEndpoint?"https:":"http:"),s.host&&s.path&&delete s.path,delete s.agent,delete s.hostname,delete s._defaultAgent,delete s.defaultPort,delete s.createConnection,r._last=!0,r.shouldKeepAlive=!1;let o=!1,f=null,u=s.timeout||this.timeout,h=_=>{r._hadError||(r.emit("error",_),r._hadError=!0)},l=()=>{f=null,o=!0;let _=new Error(`A "socket" was not created for HTTP request before ${u}ms`);_.code="ETIMEOUT",h(_)},d=_=>{o||(f!==null&&(clearTimeout(f),f=null),h(_))},m=_=>{if(o)return;if(f!=null&&(clearTimeout(f),f=null),Gp(_)){sr("Callback returned another Agent instance %o",_.constructor.name),_.addRequest(r,s);return}if(_){_.once("free",()=>{this.freeSocket(_,s)}),r.onSocket(_);return}let v=new Error(`no Duplex stream was returned to agent-base for \`${r.method} ${r.path}\``);h(v)};if(typeof this.callback!="function"){h(new Error("`callback` is not defined"));return}this.promisifiedCallback||(this.callback.length>=3?(sr("Converting legacy callback function to promise"),this.promisifiedCallback=$p.default(this.callback)):this.promisifiedCallback=this.callback),typeof u=="number"&&u>0&&(f=setTimeout(l,u)),"port"in s&&typeof s.port!="number"&&(s.port=Number(s.port));try{sr("Resolving socket for %o request: %o",s.protocol,`${r.method} ${r.path}`),Promise.resolve(this.promisifiedCallback(r,s)).then(m,d)}catch(_){Promise.reject(_).catch(d)}}freeSocket(r,n){sr("Freeing socket %o %o",r.constructor.name,n),r.destroy()}destroy(){sr("Destroying agent %o",this.constructor.name)}}t.Agent=e,t.prototype=t.Agent.prototype})($r||($r={}));Pa.exports=$r});var La=S(ar=>{"use strict";var zp=ar&&ar.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ar,"__esModule",{value:!0});var Wp=zp(Ni()),or=Wp.default("https-proxy-agent:parse-proxy-response");function Yp(t){return new Promise((e,i)=>{let r=0,n=[];function s(){let d=t.read();d?l(d):t.once("readable",s)}function o(){t.removeListener("end",u),t.removeListener("error",h),t.removeListener("close",f),t.removeListener("readable",s)}function f(d){or("onclose had error %o",d)}function u(){or("onend")}function h(d){o(),or("onerror %o",d),i(d)}function l(d){n.push(d),r+=d.length;let m=Buffer.concat(n,r);if(m.indexOf(`\r
\r
`)===-1){or("have not received end of HTTP headers yet..."),s();return}let v=m.toString("ascii",0,m.indexOf(`\r
`)),x=+v.split(" ")[1];or("got proxy server response: %o",v),e({statusCode:x,buffered:m})}t.on("error",h),t.on("close",f),t.on("end",u),s()})}ar.default=Yp});var Ma=S(pi=>{"use strict";var Kp=pi&&pi.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function f(l){try{h(r.next(l))}catch(d){o(d)}}function u(l){try{h(r.throw(l))}catch(d){o(d)}}function h(l){l.done?s(l.value):n(l.value).then(f,u)}h((r=r.apply(t,e||[])).next())})},Mi=pi&&pi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(pi,"__esModule",{value:!0});var Na=Mi(require("net")),Fa=Mi(require("tls")),Zp=Mi(require("url")),Xp=Mi(require("assert")),Jp=Mi(Ni()),Qp=jn(),ed=Mi(La()),lr=Jp.default("https-proxy-agent:agent"),qn=class extends Qp.Agent{constructor(e){let i;if(typeof e=="string"?i=Zp.default.parse(e):i=e,!i)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");lr("creating new HttpsProxyAgent instance: %o",i),super(i);let r=Object.assign({},i);this.secureProxy=i.secureProxy||rd(r.protocol),r.host=r.hostname||r.host,typeof r.port=="string"&&(r.port=parseInt(r.port,10)),!r.port&&r.host&&(r.port=this.secureProxy?443:80),this.secureProxy&&!("ALPNProtocols"in r)&&(r.ALPNProtocols=["http 1.1"]),r.host&&r.path&&(delete r.path,delete r.pathname),this.proxy=r}callback(e,i){return Kp(this,void 0,void 0,function*(){let{proxy:r,secureProxy:n}=this,s;n?(lr("Creating `tls.Socket`: %o",r),s=Fa.default.connect(r)):(lr("Creating `net.Socket`: %o",r),s=Na.default.connect(r));let o=Object.assign({},r.headers),u=`CONNECT ${`${i.host}:${i.port}`} HTTP/1.1\r
`;r.auth&&(o["Proxy-Authorization"]=`Basic ${Buffer.from(r.auth).toString("base64")}`);let{host:h,port:l,secureEndpoint:d}=i;id(l,d)||(h+=`:${l}`),o.Host=h,o.Connection="close";for(let b of Object.keys(o))u+=`${b}: ${o[b]}\r
`;let m=ed.default(s);s.write(`${u}\r
`);let{statusCode:_,buffered:v}=yield m;if(_===200){if(e.once("socket",td),i.secureEndpoint){let b=i.servername||i.host;if(!b)throw new Error('Could not determine "servername"');return lr("Upgrading socket connection to TLS"),Fa.default.connect(Object.assign(Object.assign({},nd(i,"host","hostname","path","port")),{socket:s,servername:b}))}return s}s.destroy();let x=new Na.default.Socket;return x.readable=!0,e.once("socket",b=>{lr("replaying proxy buffer for failed request"),Xp.default(b.listenerCount("data")>0),b.push(v),b.push(null)}),x})}};pi.default=qn;function td(t){t.resume()}function id(t,e){return!!(!e&&t===80||e&&t===443)}function rd(t){return typeof t=="string"?/^https:?$/i.test(t):!1}function nd(t,...e){let i={},r;for(r in t)e.includes(r)||(i[r]=t[r]);return i}});var Ua=S(($n,Da)=>{"use strict";var sd=$n&&$n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Hn=sd(Ma());function Vn(t){return new Hn.default(t)}(function(t){t.HttpsProxyAgent=Hn.default,t.prototype=Hn.default.prototype})(Vn||(Vn={}));Da.exports=Vn});var Ha=S((T_,Gr)=>{var qa=qa||function(t){return Buffer.from(t).toString("base64")};function od(t){var e=this,i=Math.round,r=Math.floor,n=new Array(64),s=new Array(64),o=new Array(64),f=new Array(64),u,h,l,d,m=new Array(65535),_=new Array(65535),v=new Array(64),x=new Array(64),b=[],T=0,B=7,E=new Array(64),R=new Array(64),k=new Array(64),X=new Array(256),A=new Array(2048),z,C=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],N=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],D=[0,1,2,3,4,5,6,7,8,9,10,11],J=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],j=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],se=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],M=[0,1,2,3,4,5,6,7,8,9,10,11],$=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],Y=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function Q(y){for(var W=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],ee=0;ee<64;ee++){var Z=r((W[ee]*y+50)/100);Z<1?Z=1:Z>255&&(Z=255),n[C[ee]]=Z}for(var oe=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],ae=0;ae<64;ae++){var xe=r((oe[ae]*y+50)/100);xe<1?xe=1:xe>255&&(xe=255),s[C[ae]]=xe}for(var ye=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Be=0,Ce=0;Ce<8;Ce++)for(var L=0;L<8;L++)o[Be]=1/(n[C[Be]]*ye[Ce]*ye[L]*8),f[Be]=1/(s[C[Be]]*ye[Ce]*ye[L]*8),Be++}function H(y,W){for(var ee=0,Z=0,oe=new Array,ae=1;ae<=16;ae++){for(var xe=1;xe<=y[ae];xe++)oe[W[Z]]=[],oe[W[Z]][0]=ee,oe[W[Z]][1]=ae,Z++,ee++;ee*=2}return oe}function we(){u=H(N,D),h=H(se,M),l=H(J,j),d=H($,Y)}function de(){for(var y=1,W=2,ee=1;ee<=15;ee++){for(var Z=y;Z<W;Z++)_[32767+Z]=ee,m[32767+Z]=[],m[32767+Z][1]=ee,m[32767+Z][0]=Z;for(var oe=-(W-1);oe<=-y;oe++)_[32767+oe]=ee,m[32767+oe]=[],m[32767+oe][1]=ee,m[32767+oe][0]=W-1+oe;y<<=1,W<<=1}}function le(){for(var y=0;y<256;y++)A[y]=19595*y,A[y+256>>0]=38470*y,A[y+512>>0]=7471*y+32768,A[y+768>>0]=-11059*y,A[y+1024>>0]=-21709*y,A[y+1280>>0]=32768*y+8421375,A[y+1536>>0]=-27439*y,A[y+1792>>0]=-5329*y}function ce(y){for(var W=y[0],ee=y[1]-1;ee>=0;)W&1<<ee&&(T|=1<<B),ee--,B--,B<0&&(T==255?(w(255),w(0)):w(T),B=7,T=0)}function w(y){b.push(y)}function K(y){w(y>>8&255),w(y&255)}function Ee(y,W){var ee,Z,oe,ae,xe,ye,Be,Ce,L=0,G,re=8,Te=64;for(G=0;G<re;++G){ee=y[L],Z=y[L+1],oe=y[L+2],ae=y[L+3],xe=y[L+4],ye=y[L+5],Be=y[L+6],Ce=y[L+7];var ne=ee+Ce,pe=ee-Ce,Se=Z+Be,te=Z-Be,be=oe+ye,Me=oe-ye,Oe=ae+xe,at=ae-xe,mt=ne+Oe,Mt=ne-Oe,Gt=Se+be,zt=Se-be;y[L]=mt+Gt,y[L+4]=mt-Gt;var ri=(zt+Mt)*.707106781;y[L+2]=Mt+ri,y[L+6]=Mt-ri,mt=at+Me,Gt=Me+te,zt=te+pe;var ni=(mt-zt)*.382683433,Oi=.5411961*mt+ni,si=1.306562965*zt+ni,oi=Gt*.707106781,ai=pe+oi,li=pe-oi;y[L+5]=li+Oi,y[L+3]=li-Oi,y[L+1]=ai+si,y[L+7]=ai-si,L+=8}for(L=0,G=0;G<re;++G){ee=y[L],Z=y[L+8],oe=y[L+16],ae=y[L+24],xe=y[L+32],ye=y[L+40],Be=y[L+48],Ce=y[L+56];var Er=ee+Ce,nr=ee-Ce,kr=Z+Be,Cr=Z-Be,Or=oe+ye,Tr=oe-ye,Ir=ae+xe,Tn=ae-xe,ci=Er+Ir,Dt=Er-Ir,ui=kr+Or,Ti=kr-Or;y[L]=ci+ui,y[L+32]=ci-ui;var Ar=(Ti+Dt)*.707106781;y[L+16]=Dt+Ar,y[L+48]=Dt-Ar,ci=Tn+Tr,ui=Tr+Cr,Ti=Cr+nr;var Br=(ci-Ti)*.382683433,Rr=.5411961*ci+Br,Pr=1.306562965*Ti+Br,At=ui*.707106781,Lr=nr+At,Nr=nr-At;y[L+40]=Nr+Rr,y[L+24]=Nr-Rr,y[L+8]=Lr+Pr,y[L+56]=Lr-Pr,L++}var Ii;for(G=0;G<Te;++G)Ii=y[G]*W[G],v[G]=Ii>0?Ii+.5|0:Ii-.5|0;return v}function _e(){K(65504),K(16),w(74),w(70),w(73),w(70),w(0),w(1),w(1),w(0),K(1),K(1),w(0),w(0)}function me(y){if(y){K(65505),y[0]===69&&y[1]===120&&y[2]===105&&y[3]===102?K(y.length+2):(K(y.length+5+2),w(69),w(120),w(105),w(102),w(0));for(var W=0;W<y.length;W++)w(y[W])}}function ge(y,W){K(65472),K(17),w(8),K(W),K(y),w(3),w(1),w(17),w(0),w(2),w(17),w(1),w(3),w(17),w(1)}function ue(){K(65499),K(132),w(0);for(var y=0;y<64;y++)w(n[y]);w(1);for(var W=0;W<64;W++)w(s[W])}function V(){K(65476),K(418),w(0);for(var y=0;y<16;y++)w(N[y+1]);for(var W=0;W<=11;W++)w(D[W]);w(16);for(var ee=0;ee<16;ee++)w(J[ee+1]);for(var Z=0;Z<=161;Z++)w(j[Z]);w(1);for(var oe=0;oe<16;oe++)w(se[oe+1]);for(var ae=0;ae<=11;ae++)w(M[ae]);w(17);for(var xe=0;xe<16;xe++)w($[xe+1]);for(var ye=0;ye<=161;ye++)w(Y[ye])}function F(y){typeof y=="undefined"||y.constructor!==Array||y.forEach(W=>{if(typeof W=="string"){K(65534);var ee=W.length;K(ee+2);var Z;for(Z=0;Z<ee;Z++)w(W.charCodeAt(Z))}})}function ke(){K(65498),K(12),w(3),w(1),w(0),w(2),w(17),w(3),w(17),w(0),w(63),w(0)}function ie(y,W,ee,Z,oe){for(var ae=oe[0],xe=oe[240],ye,Be=16,Ce=63,L=64,G=Ee(y,W),re=0;re<L;++re)x[C[re]]=G[re];var Te=x[0]-ee;ee=x[0],Te==0?ce(Z[0]):(ye=32767+Te,ce(Z[_[ye]]),ce(m[ye]));for(var ne=63;ne>0&&x[ne]==0;ne--);if(ne==0)return ce(ae),ee;for(var pe=1,Se;pe<=ne;){for(var te=pe;x[pe]==0&&pe<=ne;++pe);var be=pe-te;if(be>=Be){Se=be>>4;for(var Me=1;Me<=Se;++Me)ce(xe);be=be&15}ye=32767+x[pe],ce(oe[(be<<4)+_[ye]]),ce(m[ye]),pe++}return ne!=Ce&&ce(ae),ee}function fe(){for(var y=String.fromCharCode,W=0;W<256;W++)X[W]=y(W)}this.encode=function(y,W){var ee=new Date().getTime();W&&ot(W),b=new Array,T=0,B=7,K(65496),_e(),F(y.comments),me(y.exifBuffer),ue(),ge(y.width,y.height),V(),ke();var Z=0,oe=0,ae=0;T=0,B=7,this.encode.displayName="_encode_";for(var xe=y.data,ye=y.width,Be=y.height,Ce=ye*4,L=ye*3,G,re=0,Te,ne,pe,Se,te,be,Me,Oe;re<Be;){for(G=0;G<Ce;){for(Se=Ce*re+G,te=Se,be=-1,Me=0,Oe=0;Oe<64;Oe++)Me=Oe>>3,be=(Oe&7)*4,te=Se+Me*Ce+be,re+Me>=Be&&(te-=Ce*(re+1+Me-Be)),G+be>=Ce&&(te-=G+be-Ce+4),Te=xe[te++],ne=xe[te++],pe=xe[te++],E[Oe]=(A[Te]+A[ne+256>>0]+A[pe+512>>0]>>16)-128,R[Oe]=(A[Te+768>>0]+A[ne+1024>>0]+A[pe+1280>>0]>>16)-128,k[Oe]=(A[Te+1280>>0]+A[ne+1536>>0]+A[pe+1792>>0]>>16)-128;Z=ie(E,o,Z,u,l),oe=ie(R,f,oe,h,d),ae=ie(k,f,ae,h,d),G+=32}re+=8}if(B>=0){var at=[];at[1]=B+1,at[0]=(1<<B+1)-1,ce(at)}if(K(65497),typeof Gr=="undefined")return new Uint8Array(b);return Buffer.from(b);var mt,Mt};function ot(y){if(y<=0&&(y=1),y>100&&(y=100),z!=y){var W=0;y<50?W=Math.floor(5e3/y):W=Math.floor(200-y*2),Q(W),z=y}}function ct(){var y=new Date().getTime();t||(t=50),fe(),we(),de(),le(),ot(t);var W=new Date().getTime()-y}ct()}typeof Gr!="undefined"?Gr.exports=ja:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].encode=ja);function ja(t,e){typeof e=="undefined"&&(e=50);var i=new od(e),r=i.encode(t,e);return{data:r,width:t.width,height:t.height}}});var $a=S((I_,zn)=>{var Gn=function(){"use strict";var e=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),i=4017,r=799,n=3406,s=2276,o=1567,f=3784,u=5793,h=2896;function l(){}function d(B,E){for(var R=0,k=[],X,A,z=16;z>0&&!B[z-1];)z--;k.push({children:[],index:0});var C=k[0],N;for(X=0;X<z;X++){for(A=0;A<B[X];A++){for(C=k.pop(),C.children[C.index]=E[R];C.index>0;){if(k.length===0)throw new Error("Could not recreate Huffman Table");C=k.pop()}for(C.index++,k.push(C);k.length<=X;)k.push(N={children:[],index:0}),C.children[C.index]=N.children,C=N;R++}X+1<z&&(k.push(N={children:[],index:0}),C.children[C.index]=N.children,C=N)}return k[0].children}function m(B,E,R,k,X,A,z,C,N,D){var J=R.precision,j=R.samplesPerLine,se=R.scanLines,M=R.mcusPerLine,$=R.progressive,Y=R.maxH,Q=R.maxV,H=E,we=0,de=0;function le(){if(de>0)return de--,we>>de&1;if(we=B[E++],we==255){var L=B[E++];if(L)throw new Error("unexpected marker: "+(we<<8|L).toString(16))}return de=7,we>>>7}function ce(L){for(var G=L,re;(re=le())!==null;){if(G=G[re],typeof G=="number")return G;if(typeof G!="object")throw new Error("invalid huffman sequence")}return null}function w(L){for(var G=0;L>0;){var re=le();if(re===null)return;G=G<<1|re,L--}return G}function K(L){var G=w(L);return G>=1<<L-1?G:G+(-1<<L)+1}function Ee(L,G){var re=ce(L.huffmanTableDC),Te=re===0?0:K(re);G[0]=L.pred+=Te;for(var ne=1;ne<64;){var pe=ce(L.huffmanTableAC),Se=pe&15,te=pe>>4;if(Se===0){if(te<15)break;ne+=16;continue}ne+=te;var be=e[ne];G[be]=K(Se),ne++}}function _e(L,G){var re=ce(L.huffmanTableDC),Te=re===0?0:K(re)<<N;G[0]=L.pred+=Te}function me(L,G){G[0]|=le()<<N}var ge=0;function ue(L,G){if(ge>0){ge--;return}for(var re=A,Te=z;re<=Te;){var ne=ce(L.huffmanTableAC),pe=ne&15,Se=ne>>4;if(pe===0){if(Se<15){ge=w(Se)+(1<<Se)-1;break}re+=16;continue}re+=Se;var te=e[re];G[te]=K(pe)*(1<<N),re++}}var V=0,F;function ke(L,G){for(var re=A,Te=z,ne=0;re<=Te;){var pe=e[re],Se=G[pe]<0?-1:1;switch(V){case 0:var te=ce(L.huffmanTableAC),be=te&15,ne=te>>4;if(be===0)ne<15?(ge=w(ne)+(1<<ne),V=4):(ne=16,V=1);else{if(be!==1)throw new Error("invalid ACn encoding");F=K(be),V=ne?2:3}continue;case 1:case 2:G[pe]?G[pe]+=(le()<<N)*Se:(ne--,ne===0&&(V=V==2?3:0));break;case 3:G[pe]?G[pe]+=(le()<<N)*Se:(G[pe]=F<<N,V=0);break;case 4:G[pe]&&(G[pe]+=(le()<<N)*Se);break}re++}V===4&&(ge--,ge===0&&(V=0))}function ie(L,G,re,Te,ne){var pe=re/M|0,Se=re%M,te=pe*L.v+Te,be=Se*L.h+ne;L.blocks[te]===void 0&&D.tolerantDecoding||G(L,L.blocks[te][be])}function fe(L,G,re){var Te=re/L.blocksPerLine|0,ne=re%L.blocksPerLine;L.blocks[Te]===void 0&&D.tolerantDecoding||G(L,L.blocks[Te][ne])}var ot=k.length,ct,y,W,ee,Z,oe;$?A===0?oe=C===0?_e:me:oe=C===0?ue:ke:oe=Ee;var ae=0,xe,ye;ot==1?ye=k[0].blocksPerLine*k[0].blocksPerColumn:ye=M*R.mcusPerColumn,X||(X=ye);for(var Be,Ce;ae<ye;){for(y=0;y<ot;y++)k[y].pred=0;if(ge=0,ot==1)for(ct=k[0],Z=0;Z<X;Z++)fe(ct,oe,ae),ae++;else for(Z=0;Z<X;Z++){for(y=0;y<ot;y++)for(ct=k[y],Be=ct.h,Ce=ct.v,W=0;W<Ce;W++)for(ee=0;ee<Be;ee++)ie(ct,oe,ae,W,ee);if(ae++,ae===ye)break}if(ae===ye)do{if(B[E]===255&&B[E+1]!==0)break;E+=1}while(E<B.length-2);if(de=0,xe=B[E]<<8|B[E+1],xe<65280)throw new Error("marker was not found");if(xe>=65488&&xe<=65495)E+=2;else break}return E-H}function _(B,E){var R=[],k=E.blocksPerLine,X=E.blocksPerColumn,A=k<<3,z=new Int32Array(64),C=new Uint8Array(64);function N(H,we,de){var le=E.quantizationTable,ce,w,K,Ee,_e,me,ge,ue,V,F=de,ke;for(ke=0;ke<64;ke++)F[ke]=H[ke]*le[ke];for(ke=0;ke<8;++ke){var ie=8*ke;if(F[1+ie]==0&&F[2+ie]==0&&F[3+ie]==0&&F[4+ie]==0&&F[5+ie]==0&&F[6+ie]==0&&F[7+ie]==0){V=u*F[0+ie]+512>>10,F[0+ie]=V,F[1+ie]=V,F[2+ie]=V,F[3+ie]=V,F[4+ie]=V,F[5+ie]=V,F[6+ie]=V,F[7+ie]=V;continue}ce=u*F[0+ie]+128>>8,w=u*F[4+ie]+128>>8,K=F[2+ie],Ee=F[6+ie],_e=h*(F[1+ie]-F[7+ie])+128>>8,ue=h*(F[1+ie]+F[7+ie])+128>>8,me=F[3+ie]<<4,ge=F[5+ie]<<4,V=ce-w+1>>1,ce=ce+w+1>>1,w=V,V=K*f+Ee*o+128>>8,K=K*o-Ee*f+128>>8,Ee=V,V=_e-ge+1>>1,_e=_e+ge+1>>1,ge=V,V=ue+me+1>>1,me=ue-me+1>>1,ue=V,V=ce-Ee+1>>1,ce=ce+Ee+1>>1,Ee=V,V=w-K+1>>1,w=w+K+1>>1,K=V,V=_e*s+ue*n+2048>>12,_e=_e*n-ue*s+2048>>12,ue=V,V=me*r+ge*i+2048>>12,me=me*i-ge*r+2048>>12,ge=V,F[0+ie]=ce+ue,F[7+ie]=ce-ue,F[1+ie]=w+ge,F[6+ie]=w-ge,F[2+ie]=K+me,F[5+ie]=K-me,F[3+ie]=Ee+_e,F[4+ie]=Ee-_e}for(ke=0;ke<8;++ke){var fe=ke;if(F[8+fe]==0&&F[16+fe]==0&&F[24+fe]==0&&F[32+fe]==0&&F[40+fe]==0&&F[48+fe]==0&&F[56+fe]==0){V=u*de[ke+0]+8192>>14,F[0+fe]=V,F[8+fe]=V,F[16+fe]=V,F[24+fe]=V,F[32+fe]=V,F[40+fe]=V,F[48+fe]=V,F[56+fe]=V;continue}ce=u*F[0+fe]+2048>>12,w=u*F[32+fe]+2048>>12,K=F[16+fe],Ee=F[48+fe],_e=h*(F[8+fe]-F[56+fe])+2048>>12,ue=h*(F[8+fe]+F[56+fe])+2048>>12,me=F[24+fe],ge=F[40+fe],V=ce-w+1>>1,ce=ce+w+1>>1,w=V,V=K*f+Ee*o+2048>>12,K=K*o-Ee*f+2048>>12,Ee=V,V=_e-ge+1>>1,_e=_e+ge+1>>1,ge=V,V=ue+me+1>>1,me=ue-me+1>>1,ue=V,V=ce-Ee+1>>1,ce=ce+Ee+1>>1,Ee=V,V=w-K+1>>1,w=w+K+1>>1,K=V,V=_e*s+ue*n+2048>>12,_e=_e*n-ue*s+2048>>12,ue=V,V=me*r+ge*i+2048>>12,me=me*i-ge*r+2048>>12,ge=V,F[0+fe]=ce+ue,F[56+fe]=ce-ue,F[8+fe]=w+ge,F[48+fe]=w-ge,F[16+fe]=K+me,F[40+fe]=K-me,F[24+fe]=Ee+_e,F[32+fe]=Ee-_e}for(ke=0;ke<64;++ke){var ot=128+(F[ke]+8>>4);we[ke]=ot<0?0:ot>255?255:ot}}T(A*X*8);for(var D,J,j=0;j<X;j++){var se=j<<3;for(D=0;D<8;D++)R.push(new Uint8Array(A));for(var M=0;M<k;M++){N(E.blocks[j][M],C,z);var $=0,Y=M<<3;for(J=0;J<8;J++){var Q=R[se+J];for(D=0;D<8;D++)Q[Y+D]=C[$++]}}}return R}function v(B){return B<0?0:B>255?255:B}l.prototype={load:function(E){var R=new XMLHttpRequest;R.open("GET",E,!0),R.responseType="arraybuffer",R.onload=function(){var k=new Uint8Array(R.response||R.mozResponseArrayBuffer);this.parse(k),this.onload&&this.onload()}.bind(this),R.send(null)},parse:function(E){var R=this.opts.maxResolutionInMP*1e3*1e3,k=0,X=E.length;function A(){var te=E[k]<<8|E[k+1];return k+=2,te}function z(){var te=A(),be=E.subarray(k,k+te-2);return k+=be.length,be}function C(te){var be=1,Me=1,Oe,at;for(at in te.components)te.components.hasOwnProperty(at)&&(Oe=te.components[at],be<Oe.h&&(be=Oe.h),Me<Oe.v&&(Me=Oe.v));var mt=Math.ceil(te.samplesPerLine/8/be),Mt=Math.ceil(te.scanLines/8/Me);for(at in te.components)if(te.components.hasOwnProperty(at)){Oe=te.components[at];var Gt=Math.ceil(Math.ceil(te.samplesPerLine/8)*Oe.h/be),zt=Math.ceil(Math.ceil(te.scanLines/8)*Oe.v/Me),ri=mt*Oe.h,ni=Mt*Oe.v,Oi=ni*ri,si=[];T(Oi*256);for(var oi=0;oi<ni;oi++){for(var ai=[],li=0;li<ri;li++)ai.push(new Int32Array(64));si.push(ai)}Oe.blocksPerLine=Gt,Oe.blocksPerColumn=zt,Oe.blocks=si}te.maxH=be,te.maxV=Me,te.mcusPerLine=mt,te.mcusPerColumn=Mt}var N=null,D=null,J=null,j,se,M=[],$=[],Y=[],Q=[],H=A(),we=-1;if(this.comments=[],H!=65496)throw new Error("SOI not found");for(H=A();H!=65497;){var de,le,ce;switch(H){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var w=z();if(H===65534){var K=String.fromCharCode.apply(null,w);this.comments.push(K)}H===65504&&w[0]===74&&w[1]===70&&w[2]===73&&w[3]===70&&w[4]===0&&(N={version:{major:w[5],minor:w[6]},densityUnits:w[7],xDensity:w[8]<<8|w[9],yDensity:w[10]<<8|w[11],thumbWidth:w[12],thumbHeight:w[13],thumbData:w.subarray(14,14+3*w[12]*w[13])}),H===65505&&w[0]===69&&w[1]===120&&w[2]===105&&w[3]===102&&w[4]===0&&(this.exifBuffer=w.subarray(5,w.length)),H===65518&&w[0]===65&&w[1]===100&&w[2]===111&&w[3]===98&&w[4]===101&&w[5]===0&&(D={version:w[6],flags0:w[7]<<8|w[8],flags1:w[9]<<8|w[10],transformCode:w[11]});break;case 65499:for(var Ee=A(),_e=Ee+k-2;k<_e;){var me=E[k++];T(256);var ge=new Int32Array(64);if(me>>4)if(me>>4===1)for(le=0;le<64;le++){var ue=e[le];ge[ue]=A()}else throw new Error("DQT: invalid table spec");else for(le=0;le<64;le++){var ue=e[le];ge[ue]=E[k++]}M[me&15]=ge}break;case 65472:case 65473:case 65474:A(),j={},j.extended=H===65473,j.progressive=H===65474,j.precision=E[k++],j.scanLines=A(),j.samplesPerLine=A(),j.components={},j.componentsOrder=[];var V=j.scanLines*j.samplesPerLine;if(V>R){var F=Math.ceil((V-R)/1e6);throw new Error(`maxResolutionInMP limit exceeded by ${F}MP`)}var ke=E[k++],ie,fe=0,ot=0;for(de=0;de<ke;de++){ie=E[k];var ct=E[k+1]>>4,y=E[k+1]&15,W=E[k+2];if(ct<=0||y<=0)throw new Error("Invalid sampling factor, expected values above 0");j.componentsOrder.push(ie),j.components[ie]={h:ct,v:y,quantizationIdx:W},k+=3}C(j),$.push(j);break;case 65476:var ee=A();for(de=2;de<ee;){var Z=E[k++],oe=new Uint8Array(16),ae=0;for(le=0;le<16;le++,k++)ae+=oe[le]=E[k];T(16+ae);var xe=new Uint8Array(ae);for(le=0;le<ae;le++,k++)xe[le]=E[k];de+=17+ae,(Z>>4?Y:Q)[Z&15]=d(oe,xe)}break;case 65501:A(),se=A();break;case 65500:A(),A();break;case 65498:var ye=A(),Be=E[k++],Ce=[],L;for(de=0;de<Be;de++){L=j.components[E[k++]];var G=E[k++];L.huffmanTableDC=Q[G>>4],L.huffmanTableAC=Y[G&15],Ce.push(L)}var re=E[k++],Te=E[k++],ne=E[k++],pe=m(E,k,j,Ce,se,re,Te,ne>>4,ne&15,this.opts);k+=pe;break;case 65535:E[k]!==255&&k--;break;default:if(E[k-3]==255&&E[k-2]>=192&&E[k-2]<=254){k-=3;break}else if(H===224||H==225){if(we!==-1)throw new Error(`first unknown JPEG marker at offset ${we.toString(16)}, second unknown JPEG marker ${H.toString(16)} at offset ${(k-1).toString(16)}`);we=k-1;let te=A();if(E[k+te-2]===255){k+=te-2;break}}throw new Error("unknown JPEG marker "+H.toString(16))}H=A()}if($.length!=1)throw new Error("only single frame JPEGs supported");for(var de=0;de<$.length;de++){var Se=$[de].components;for(var le in Se)Se[le].quantizationTable=M[Se[le].quantizationIdx],delete Se[le].quantizationIdx}this.width=j.samplesPerLine,this.height=j.scanLines,this.jfif=N,this.adobe=D,this.components=[];for(var de=0;de<j.componentsOrder.length;de++){var L=j.components[j.componentsOrder[de]];this.components.push({lines:_(j,L),scaleX:L.h/j.maxH,scaleY:L.v/j.maxV})}},getData:function(E,R){var k=this.width/E,X=this.height/R,A,z,C,N,D,J,j,se,M,$,Y=0,Q,H,we,de,le,ce,w,K,Ee,_e,me,ge=E*R*this.components.length;T(ge);var ue=new Uint8Array(ge);switch(this.components.length){case 1:for(A=this.components[0],$=0;$<R;$++)for(D=A.lines[0|$*A.scaleY*X],M=0;M<E;M++)Q=D[0|M*A.scaleX*k],ue[Y++]=Q;break;case 2:for(A=this.components[0],z=this.components[1],$=0;$<R;$++)for(D=A.lines[0|$*A.scaleY*X],J=z.lines[0|$*z.scaleY*X],M=0;M<E;M++)Q=D[0|M*A.scaleX*k],ue[Y++]=Q,Q=J[0|M*z.scaleX*k],ue[Y++]=Q;break;case 3:for(me=!0,this.adobe&&this.adobe.transformCode?me=!0:typeof this.opts.colorTransform!="undefined"&&(me=!!this.opts.colorTransform),A=this.components[0],z=this.components[1],C=this.components[2],$=0;$<R;$++)for(D=A.lines[0|$*A.scaleY*X],J=z.lines[0|$*z.scaleY*X],j=C.lines[0|$*C.scaleY*X],M=0;M<E;M++)me?(Q=D[0|M*A.scaleX*k],H=J[0|M*z.scaleX*k],we=j[0|M*C.scaleX*k],K=v(Q+1.402*(we-128)),Ee=v(Q-.3441363*(H-128)-.71413636*(we-128)),_e=v(Q+1.772*(H-128))):(K=D[0|M*A.scaleX*k],Ee=J[0|M*z.scaleX*k],_e=j[0|M*C.scaleX*k]),ue[Y++]=K,ue[Y++]=Ee,ue[Y++]=_e;break;case 4:if(!this.adobe)throw new Error("Unsupported color mode (4 components)");for(me=!1,this.adobe&&this.adobe.transformCode?me=!0:typeof this.opts.colorTransform!="undefined"&&(me=!!this.opts.colorTransform),A=this.components[0],z=this.components[1],C=this.components[2],N=this.components[3],$=0;$<R;$++)for(D=A.lines[0|$*A.scaleY*X],J=z.lines[0|$*z.scaleY*X],j=C.lines[0|$*C.scaleY*X],se=N.lines[0|$*N.scaleY*X],M=0;M<E;M++)me?(Q=D[0|M*A.scaleX*k],H=J[0|M*z.scaleX*k],we=j[0|M*C.scaleX*k],de=se[0|M*N.scaleX*k],le=255-v(Q+1.402*(we-128)),ce=255-v(Q-.3441363*(H-128)-.71413636*(we-128)),w=255-v(Q+1.772*(H-128))):(le=D[0|M*A.scaleX*k],ce=J[0|M*z.scaleX*k],w=j[0|M*C.scaleX*k],de=se[0|M*N.scaleX*k]),ue[Y++]=255-le,ue[Y++]=255-ce,ue[Y++]=255-w,ue[Y++]=255-de;break;default:throw new Error("Unsupported color mode")}return ue},copyToImageData:function(E,R){var k=E.width,X=E.height,A=E.data,z=this.getData(k,X),C=0,N=0,D,J,j,se,M,$,Y,Q,H;switch(this.components.length){case 1:for(J=0;J<X;J++)for(D=0;D<k;D++)j=z[C++],A[N++]=j,A[N++]=j,A[N++]=j,R&&(A[N++]=255);break;case 3:for(J=0;J<X;J++)for(D=0;D<k;D++)Y=z[C++],Q=z[C++],H=z[C++],A[N++]=Y,A[N++]=Q,A[N++]=H,R&&(A[N++]=255);break;case 4:for(J=0;J<X;J++)for(D=0;D<k;D++)M=z[C++],$=z[C++],j=z[C++],se=z[C++],Y=255-v(M*(1-se/255)+se),Q=255-v($*(1-se/255)+se),H=255-v(j*(1-se/255)+se),A[N++]=Y,A[N++]=Q,A[N++]=H,R&&(A[N++]=255);break;default:throw new Error("Unsupported color mode")}}};var x=0,b=0;function T(B=0){var E=x+B;if(E>b){var R=Math.ceil((E-b)/1024/1024);throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${R}MB`)}x=E}return l.resetMaxMemoryUsage=function(B){x=0,b=B},l.getBytesAllocated=function(){return x},l.requestMemoryAllocation=T,l}();typeof zn!="undefined"?zn.exports=Va:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].decode=Va);function Va(t,e={}){var i={colorTransform:void 0,useTArray:!1,formatAsRGBA:!0,tolerantDecoding:!0,maxResolutionInMP:100,maxMemoryUsageInMB:512},r={...i,...e},n=new Uint8Array(t),s=new Gn;s.opts=r,Gn.resetMaxMemoryUsage(r.maxMemoryUsageInMB*1024*1024),s.parse(n);var o=r.formatAsRGBA?4:3,f=s.width*s.height*o;try{Gn.requestMemoryAllocation(f);var u={width:s.width,height:s.height,exifBuffer:s.exifBuffer,data:r.useTArray?new Uint8Array(f):Buffer.alloc(f)};s.comments.length>0&&(u.comments=s.comments)}catch(h){throw h instanceof RangeError?new Error("Could not allocate enough memory for the image. Required: "+f):h instanceof ReferenceError&&h.message==="Buffer is not defined"?new Error("Buffer is not globally defined in this environment. Consider setting useTArray to true"):h}return s.copyToImageData(u,r.formatAsRGBA),u}});var za=S((A_,Ga)=>{var ad=Ha(),ld=$a();Ga.exports={encode:ad,decode:ld}});var Ya=S((B_,Wa)=>{"use strict";function zr(){this._types=Object.create(null),this._extensions=Object.create(null);for(let t=0;t<arguments.length;t++)this.define(arguments[t]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}zr.prototype.define=function(t,e){for(let i in t){let r=t[i].map(function(n){return n.toLowerCase()});i=i.toLowerCase();for(let n=0;n<r.length;n++){let s=r[n];if(s[0]!=="*"){if(!e&&s in this._types)throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+i+'".');this._types[s]=i}}if(e||!this._extensions[i]){let n=r[0];this._extensions[i]=n[0]!=="*"?n:n.substr(1)}}};zr.prototype.getType=function(t){t=String(t);let e=t.replace(/^.*[/\\]/,"").toLowerCase(),i=e.replace(/^.*\./,"").toLowerCase(),r=e.length<t.length;return(i.length<e.length-1||!r)&&this._types[i]||null};zr.prototype.getExtension=function(t){return t=/^\s*([^;\s]*)/.test(t)&&RegExp.$1,t&&this._extensions[t.toLowerCase()]||null};Wa.exports=zr});var Za=S((R_,Ka)=>{Ka.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}});var Ja=S((P_,Xa)=>{Xa.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}});var el=S((L_,Qa)=>{"use strict";var cd=Ya();Qa.exports=new cd(Za(),Ja())});var il=S((N_,tl)=>{tl.exports=function(t,e){for(var i=[],r=0;r<t.length;r++){var n=e(t[r],r);ud(n)?i.push.apply(i,n):i.push(n)}return i};var ud=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"}});var al=S((F_,ol)=>{"use strict";ol.exports=nl;function nl(t,e,i){t instanceof RegExp&&(t=rl(t,i)),e instanceof RegExp&&(e=rl(e,i));var r=sl(t,e,i);return r&&{start:r[0],end:r[1],pre:i.slice(0,r[0]),body:i.slice(r[0]+t.length,r[1]),post:i.slice(r[1]+e.length)}}function rl(t,e){var i=e.match(t);return i?i[0]:null}nl.range=sl;function sl(t,e,i){var r,n,s,o,f,u=i.indexOf(t),h=i.indexOf(e,u+1),l=u;if(u>=0&&h>0){if(t===e)return[u,h];for(r=[],s=i.length;l>=0&&!f;)l==u?(r.push(l),u=i.indexOf(t,l+1)):r.length==1?f=[r.pop(),h]:(n=r.pop(),n<s&&(s=n,o=h),h=i.indexOf(e,l+1)),l=u<h&&u>=0?u:h;r.length&&(f=[s,o])}return f}});var ml=S((M_,dl)=>{var fd=il(),ll=al();dl.exports=dd;var cl="\0SLASH"+Math.random()+"\0",ul="\0OPEN"+Math.random()+"\0",Yn="\0CLOSE"+Math.random()+"\0",fl="\0COMMA"+Math.random()+"\0",hl="\0PERIOD"+Math.random()+"\0";function Wn(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function hd(t){return t.split("\\\\").join(cl).split("\\{").join(ul).split("\\}").join(Yn).split("\\,").join(fl).split("\\.").join(hl)}function pd(t){return t.split(cl).join("\\").split(ul).join("{").split(Yn).join("}").split(fl).join(",").split(hl).join(".")}function pl(t){if(!t)return[""];var e=[],i=ll("{","}",t);if(!i)return t.split(",");var r=i.pre,n=i.body,s=i.post,o=r.split(",");o[o.length-1]+="{"+n+"}";var f=pl(s);return s.length&&(o[o.length-1]+=f.shift(),o.push.apply(o,f)),e.push.apply(e,o),e}function dd(t){return t?(t.substr(0,2)==="{}"&&(t="\\{\\}"+t.substr(2)),Di(hd(t),!0).map(pd)):[]}function md(t){return"{"+t+"}"}function gd(t){return/^-?0\d/.test(t)}function vd(t,e){return t<=e}function _d(t,e){return t>=e}function Di(t,e){var i=[],r=ll("{","}",t);if(!r||/\$$/.test(r.pre))return[t];var n=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),s=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),o=n||s,f=r.body.indexOf(",")>=0;if(!o&&!f)return r.post.match(/,.*\}/)?(t=r.pre+"{"+r.body+Yn+r.post,Di(t)):[t];var u;if(o)u=r.body.split(/\.\./);else if(u=pl(r.body),u.length===1&&(u=Di(u[0],!1).map(md),u.length===1)){var l=r.post.length?Di(r.post,!1):[""];return l.map(function(D){return r.pre+u[0]+D})}var h=r.pre,l=r.post.length?Di(r.post,!1):[""],d;if(o){var m=Wn(u[0]),_=Wn(u[1]),v=Math.max(u[0].length,u[1].length),x=u.length==3?Math.abs(Wn(u[2])):1,b=vd,T=_<m;T&&(x*=-1,b=_d);var B=u.some(gd);d=[];for(var E=m;b(E,_);E+=x){var R;if(s)R=String.fromCharCode(E),R==="\\"&&(R="");else if(R=String(E),B){var k=v-R.length;if(k>0){var X=new Array(k+1).join("0");E<0?R="-"+X+R.slice(1):R=X+R}}d.push(R)}}else d=fd(u,function(N){return Di(N,!1)});for(var A=0;A<d.length;A++)for(var z=0;z<l.length;z++){var C=h+d[A]+l[z];(!e||o||C)&&i.push(C)}return i}});var bl=S((D_,yl)=>{yl.exports=pt;pt.Minimatch=Ye;var cr=function(){try{return require("path")}catch{}}()||{sep:"/"};pt.sep=cr.sep;var Xn=pt.GLOBSTAR=Ye.GLOBSTAR={},xd=ml(),gl={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},Kn="[^/]",Zn=Kn+"*?",yd="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",bd="(?:(?!(?:\\/|^)\\.).)*?",vl=wd("().*{}+?[]^$\\!");function wd(t){return t.split("").reduce(function(e,i){return e[i]=!0,e},{})}var _l=/\/+/;pt.filter=Sd;function Sd(t,e){return e=e||{},function(i,r,n){return pt(i,t,e)}}function Yt(t,e){e=e||{};var i={};return Object.keys(t).forEach(function(r){i[r]=t[r]}),Object.keys(e).forEach(function(r){i[r]=e[r]}),i}pt.defaults=function(t){if(!t||typeof t!="object"||!Object.keys(t).length)return pt;var e=pt,i=function(n,s,o){return e(n,s,Yt(t,o))};return i.Minimatch=function(n,s){return new e.Minimatch(n,Yt(t,s))},i.Minimatch.defaults=function(n){return e.defaults(Yt(t,n)).Minimatch},i.filter=function(n,s){return e.filter(n,Yt(t,s))},i.defaults=function(n){return e.defaults(Yt(t,n))},i.makeRe=function(n,s){return e.makeRe(n,Yt(t,s))},i.braceExpand=function(n,s){return e.braceExpand(n,Yt(t,s))},i.match=function(r,n,s){return e.match(r,n,Yt(t,s))},i};Ye.defaults=function(t){return pt.defaults(t).Minimatch};function pt(t,e,i){return Yr(e),i||(i={}),!i.nocomment&&e.charAt(0)==="#"?!1:new Ye(e,i).match(t)}function Ye(t,e){if(!(this instanceof Ye))return new Ye(t,e);Yr(t),e||(e={}),t=t.trim(),!e.allowWindowsEscape&&cr.sep!=="/"&&(t=t.split(cr.sep).join("/")),this.options=e,this.set=[],this.pattern=t,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.make()}Ye.prototype.debug=function(){};Ye.prototype.make=Ed;function Ed(){var t=this.pattern,e=this.options;if(!e.nocomment&&t.charAt(0)==="#"){this.comment=!0;return}if(!t){this.empty=!0;return}this.parseNegate();var i=this.globSet=this.braceExpand();e.debug&&(this.debug=function(){console.error.apply(console,arguments)}),this.debug(this.pattern,i),i=this.globParts=i.map(function(r){return r.split(_l)}),this.debug(this.pattern,i),i=i.map(function(r,n,s){return r.map(this.parse,this)},this),this.debug(this.pattern,i),i=i.filter(function(r){return r.indexOf(!1)===-1}),this.debug(this.pattern,i),this.set=i}Ye.prototype.parseNegate=kd;function kd(){var t=this.pattern,e=!1,i=this.options,r=0;if(!i.nonegate){for(var n=0,s=t.length;n<s&&t.charAt(n)==="!";n++)e=!e,r++;r&&(this.pattern=t.substr(r)),this.negate=e}}pt.braceExpand=function(t,e){return xl(t,e)};Ye.prototype.braceExpand=xl;function xl(t,e){return e||(this instanceof Ye?e=this.options:e={}),t=typeof t=="undefined"?this.pattern:t,Yr(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:xd(t)}var Cd=1024*64,Yr=function(t){if(typeof t!="string")throw new TypeError("invalid pattern");if(t.length>Cd)throw new TypeError("pattern is too long")};Ye.prototype.parse=Od;var Wr={};function Od(t,e){Yr(t);var i=this.options;if(t==="**")if(i.noglobstar)t="*";else return Xn;if(t==="")return"";var r="",n=!!i.nocase,s=!1,o=[],f=[],u,h=!1,l=-1,d=-1,m=t.charAt(0)==="."?"":i.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",_=this;function v(){if(u){switch(u){case"*":r+=Zn,n=!0;break;case"?":r+=Kn,n=!0;break;default:r+="\\"+u;break}_.debug("clearStateChar %j %j",u,r),u=!1}}for(var x=0,b=t.length,T;x<b&&(T=t.charAt(x));x++){if(this.debug("%s	%s %s %j",t,x,r,T),s&&vl[T]){r+="\\"+T,s=!1;continue}switch(T){case"/":return!1;case"\\":v(),s=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s	%s %s %j <-- stateChar",t,x,r,T),h){this.debug("  in class"),T==="!"&&x===d+1&&(T="^"),r+=T;continue}_.debug("call clearStateChar %j",u),v(),u=T,i.noext&&v();continue;case"(":if(h){r+="(";continue}if(!u){r+="\\(";continue}o.push({type:u,start:x-1,reStart:r.length,open:gl[u].open,close:gl[u].close}),r+=u==="!"?"(?:(?!(?:":"(?:",this.debug("plType %j %j",u,r),u=!1;continue;case")":if(h||!o.length){r+="\\)";continue}v(),n=!0;var B=o.pop();r+=B.close,B.type==="!"&&f.push(B),B.reEnd=r.length;continue;case"|":if(h||!o.length||s){r+="\\|",s=!1;continue}v(),r+="|";continue;case"[":if(v(),h){r+="\\"+T;continue}h=!0,d=x,l=r.length,r+=T;continue;case"]":if(x===d+1||!h){r+="\\"+T,s=!1;continue}var E=t.substring(d+1,x);try{RegExp("["+E+"]")}catch{var R=this.parse(E,Wr);r=r.substr(0,l)+"\\["+R[0]+"\\]",n=n||R[1],h=!1;continue}n=!0,h=!1,r+=T;continue;default:v(),s?s=!1:vl[T]&&!(T==="^"&&h)&&(r+="\\"),r+=T}}for(h&&(E=t.substr(d+1),R=this.parse(E,Wr),r=r.substr(0,l)+"\\["+R[0],n=n||R[1]),B=o.pop();B;B=o.pop()){var k=r.slice(B.reStart+B.open.length);this.debug("setting tail",r,B),k=k.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(we,de,le){return le||(le="\\"),de+de+le+"|"}),this.debug(`tail=%j
   %s`,k,k,B,r);var X=B.type==="*"?Zn:B.type==="?"?Kn:"\\"+B.type;n=!0,r=r.slice(0,B.reStart)+X+"\\("+k}v(),s&&(r+="\\\\");var A=!1;switch(r.charAt(0)){case"[":case".":case"(":A=!0}for(var z=f.length-1;z>-1;z--){var C=f[z],N=r.slice(0,C.reStart),D=r.slice(C.reStart,C.reEnd-8),J=r.slice(C.reEnd-8,C.reEnd),j=r.slice(C.reEnd);J+=j;var se=N.split("(").length-1,M=j;for(x=0;x<se;x++)M=M.replace(/\)[+*?]?/,"");j=M;var $="";j===""&&e!==Wr&&($="$");var Y=N+D+j+$+J;r=Y}if(r!==""&&n&&(r="(?=.)"+r),A&&(r=m+r),e===Wr)return[r,n];if(!n)return Id(t);var Q=i.nocase?"i":"";try{var H=new RegExp("^"+r+"$",Q)}catch{return new RegExp("$.")}return H._glob=t,H._src=r,H}pt.makeRe=function(t,e){return new Ye(t,e||{}).makeRe()};Ye.prototype.makeRe=Td;function Td(){if(this.regexp||this.regexp===!1)return this.regexp;var t=this.set;if(!t.length)return this.regexp=!1,this.regexp;var e=this.options,i=e.noglobstar?Zn:e.dot?yd:bd,r=e.nocase?"i":"",n=t.map(function(s){return s.map(function(o){return o===Xn?i:typeof o=="string"?Ad(o):o._src}).join("\\/")}).join("|");n="^(?:"+n+")$",this.negate&&(n="^(?!"+n+").*$");try{this.regexp=new RegExp(n,r)}catch{this.regexp=!1}return this.regexp}pt.match=function(t,e,i){i=i||{};var r=new Ye(e,i);return t=t.filter(function(n){return r.match(n)}),r.options.nonull&&!t.length&&t.push(e),t};Ye.prototype.match=function(e,i){if(typeof i=="undefined"&&(i=this.partial),this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&i)return!0;var r=this.options;cr.sep!=="/"&&(e=e.split(cr.sep).join("/")),e=e.split(_l),this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var s,o;for(o=e.length-1;o>=0&&(s=e[o],!s);o--);for(o=0;o<n.length;o++){var f=n[o],u=e;r.matchBase&&f.length===1&&(u=[s]);var h=this.matchOne(u,f,i);if(h)return r.flipNegate?!0:!this.negate}return r.flipNegate?!1:this.negate};Ye.prototype.matchOne=function(t,e,i){var r=this.options;this.debug("matchOne",{this:this,file:t,pattern:e}),this.debug("matchOne",t.length,e.length);for(var n=0,s=0,o=t.length,f=e.length;n<o&&s<f;n++,s++){this.debug("matchOne loop");var u=e[s],h=t[n];if(this.debug(e,u,h),u===!1)return!1;if(u===Xn){this.debug("GLOBSTAR",[e,u,h]);var l=n,d=s+1;if(d===f){for(this.debug("** at the end");n<o;n++)if(t[n]==="."||t[n]===".."||!r.dot&&t[n].charAt(0)===".")return!1;return!0}for(;l<o;){var m=t[l];if(this.debug(`
globstar while`,t,l,e,d,m),this.matchOne(t.slice(l),e.slice(d),i))return this.debug("globstar found match!",l,o,m),!0;if(m==="."||m===".."||!r.dot&&m.charAt(0)==="."){this.debug("dot detected!",t,l,e,d);break}this.debug("globstar swallow a segment, and continue"),l++}return!!(i&&(this.debug(`
>>> no match, partial?`,t,l,e,d),l===o))}var _;if(typeof u=="string"?(_=h===u,this.debug("string match",u,h,_)):(_=h.match(u),this.debug("pattern match",u,h,_)),!_)return!1}if(n===o&&s===f)return!0;if(n===o)return i;if(s===f)return n===o-1&&t[n]==="";throw new Error("wtf?")};function Id(t){return t.replace(/\\(.)/g,"$1")}function Ad(t){return t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}});var Qn=S((U_,Sl)=>{"use strict";var wl=require("fs"),Jn;function Bd(){try{return wl.statSync("/.dockerenv"),!0}catch{return!1}}function Rd(){try{return wl.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Sl.exports=()=>(Jn===void 0&&(Jn=Bd()||Rd()),Jn)});var Cl=S((j_,es)=>{"use strict";var Pd=require("os"),Ld=require("fs"),El=Qn(),kl=()=>{if(process.platform!=="linux")return!1;if(Pd.release().toLowerCase().includes("microsoft"))return!El();try{return Ld.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!El():!1}catch{return!1}};process.env.__IS_WSL_TEST__?es.exports=kl:es.exports=kl()});var Tl=S((q_,Ol)=>{"use strict";Ol.exports=(t,e,i)=>{let r=n=>Object.defineProperty(t,e,{value:n,enumerable:!0,writable:!0});return Object.defineProperty(t,e,{configurable:!0,enumerable:!0,get(){let n=i();return r(n),n},set(n){r(n)}}),t}});var Nl=S((H_,Ll)=>{var Nd=require("path"),Fd=require("child_process"),{promises:ts,constants:Pl}=require("fs"),Kr=Cl(),Md=Qn(),is=Tl(),Il=Nd.join(__dirname,"xdg-open"),{platform:Ui,arch:Al}=process,Dd=(()=>{let t="/mnt/",e;return async function(){if(e)return e;let i="/etc/wsl.conf",r=!1;try{await ts.access(i,Pl.F_OK),r=!0}catch{}if(!r)return t;let n=await ts.readFile(i,{encoding:"utf8"}),s=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(n);return s?(e=s.groups.mountPoint.trim(),e=e.endsWith("/")?e:`${e}/`,e):t}})(),Bl=async(t,e)=>{let i;for(let r of t)try{return await e(r)}catch(n){i=n}throw i},Zr=async t=>{if(t={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...t},Array.isArray(t.app))return Bl(t.app,f=>Zr({...t,app:f}));let{name:e,arguments:i=[]}=t.app||{};if(i=[...i],Array.isArray(e))return Bl(e,f=>Zr({...t,app:{name:f,arguments:i}}));let r,n=[],s={};if(Ui==="darwin")r="open",t.wait&&n.push("--wait-apps"),t.background&&n.push("--background"),t.newInstance&&n.push("--new"),e&&n.push("-a",e);else if(Ui==="win32"||Kr&&!Md()){let f=await Dd();r=Kr?`${f}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,n.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),Kr||(s.windowsVerbatimArguments=!0);let u=["Start"];t.wait&&u.push("-Wait"),e?(u.push(`"\`"${e}\`""`,"-ArgumentList"),t.target&&i.unshift(t.target)):t.target&&u.push(`"${t.target}"`),i.length>0&&(i=i.map(h=>`"\`"${h}\`""`),u.push(i.join(","))),t.target=Buffer.from(u.join(" "),"utf16le").toString("base64")}else{if(e)r=e;else{let f=!__dirname||__dirname==="/",u=!1;try{await ts.access(Il,Pl.X_OK),u=!0}catch{}r=process.versions.electron||Ui==="android"||f||!u?"xdg-open":Il}i.length>0&&n.push(...i),t.wait||(s.stdio="ignore",s.detached=!0)}t.target&&n.push(t.target),Ui==="darwin"&&i.length>0&&n.push("--args",...i);let o=Fd.spawn(r,n,s);return t.wait?new Promise((f,u)=>{o.once("error",u),o.once("close",h=>{if(t.allowNonzeroExitCode&&h>0){u(new Error(`Exited with code ${h}`));return}f(o)})}):(o.unref(),o)},rs=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `target`");return Zr({...e,target:t})},Ud=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `name`");let{arguments:i=[]}=e||{};if(i!=null&&!Array.isArray(i))throw new TypeError("Expected `appArguments` as Array type");return Zr({...e,app:{name:t,arguments:i}})};function Rl(t){if(typeof t=="string"||Array.isArray(t))return t;let{[Al]:e}=t;if(!e)throw new Error(`${Al} is not supported`);return e}function ns({[Ui]:t},{wsl:e}){if(e&&Kr)return Rl(e);if(!t)throw new Error(`${Ui} is not supported`);return Rl(t)}var Xr={};is(Xr,"chrome",()=>ns({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));is(Xr,"firefox",()=>ns({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));is(Xr,"edge",()=>ns({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));rs.apps=Xr;rs.openApp=Ud;Ll.exports=rs});var ss=S((V_,Ml)=>{"use strict";var jd=require("util"),Fl=require("stream"),Ct=Ml.exports=function(){Fl.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};jd.inherits(Ct,Fl);Ct.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))};Ct.prototype.write=function(t,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let i;return Buffer.isBuffer(t)?i=t:i=Buffer.from(t,e||this._encoding),this._buffers.push(i),this._buffered+=i.length,this._process(),this._reads&&this._reads.length===0&&(this._paused=!0),this.writable&&!this._paused};Ct.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(this._buffers.length===0?this._end():(this._buffers.push(null),this._process()))};Ct.prototype.destroySoon=Ct.prototype.end;Ct.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()};Ct.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))};Ct.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))};Ct.prototype._processRead=function(t){this._reads.shift();let e=0,i=0,r=Buffer.alloc(t.length);for(;e<t.length;){let n=this._buffers[i++],s=Math.min(n.length,t.length-e);n.copy(r,e,0,s),e+=s,s!==n.length&&(this._buffers[--i]=n.slice(s))}i>0&&this._buffers.splice(0,i),this._buffered-=t.length,t.func.call(this,r)};Ct.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}});var as=S(os=>{"use strict";var Kt=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];os.getImagePasses=function(t,e){let i=[],r=t%8,n=e%8,s=(t-r)/8,o=(e-n)/8;for(let f=0;f<Kt.length;f++){let u=Kt[f],h=s*u.x.length,l=o*u.y.length;for(let d=0;d<u.x.length&&u.x[d]<r;d++)h++;for(let d=0;d<u.y.length&&u.y[d]<n;d++)l++;h>0&&l>0&&i.push({width:h,height:l,index:f})}return i};os.getInterlaceIterator=function(t){return function(e,i,r){let n=e%Kt[r].x.length,s=(e-n)/Kt[r].x.length*8+Kt[r].x[n],o=i%Kt[r].y.length,f=(i-o)/Kt[r].y.length*8+Kt[r].y[o];return s*4+f*t*4}}});var ls=S((G_,Dl)=>{"use strict";Dl.exports=function(e,i,r){let n=e+i-r,s=Math.abs(n-e),o=Math.abs(n-i),f=Math.abs(n-r);return s<=o&&s<=f?e:o<=f?i:r}});var cs=S((z_,jl)=>{"use strict";var qd=as(),Hd=ls();function Ul(t,e,i){let r=t*e;return i!==8&&(r=Math.ceil(r/(8/i))),r}var ji=jl.exports=function(t,e){let i=t.width,r=t.height,n=t.interlace,s=t.bpp,o=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],n){let f=qd.getImagePasses(i,r);for(let u=0;u<f.length;u++)this._images.push({byteWidth:Ul(f[u].width,s,o),height:f[u].height,lineIndex:0})}else this._images.push({byteWidth:Ul(i,s,o),height:r,lineIndex:0});o===8?this._xComparison=s:o===16?this._xComparison=s*2:this._xComparison=1};ji.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))};ji.prototype._unFilterType1=function(t,e,i){let r=this._xComparison,n=r-1;for(let s=0;s<i;s++){let o=t[1+s],f=s>n?e[s-r]:0;e[s]=o+f}};ji.prototype._unFilterType2=function(t,e,i){let r=this._lastLine;for(let n=0;n<i;n++){let s=t[1+n],o=r?r[n]:0;e[n]=s+o}};ji.prototype._unFilterType3=function(t,e,i){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<i;o++){let f=t[1+o],u=s?s[o]:0,h=o>n?e[o-r]:0,l=Math.floor((h+u)/2);e[o]=f+l}};ji.prototype._unFilterType4=function(t,e,i){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<i;o++){let f=t[1+o],u=s?s[o]:0,h=o>n?e[o-r]:0,l=o>n&&s?s[o-r]:0,d=Hd(h,u,l);e[o]=f+d}};ji.prototype._reverseFilterLine=function(t){let e=t[0],i,r=this._images[this._imageIndex],n=r.byteWidth;if(e===0)i=t.slice(1,n+1);else switch(i=Buffer.alloc(n),e){case 1:this._unFilterType1(t,i,n);break;case 2:this._unFilterType2(t,i,n);break;case 3:this._unFilterType3(t,i,n);break;case 4:this._unFilterType4(t,i,n);break;default:throw new Error("Unrecognised filter type - "+e)}this.write(i),r.lineIndex++,r.lineIndex>=r.height?(this._lastLine=null,this._imageIndex++,r=this._images[this._imageIndex]):this._lastLine=i,r?this.read(r.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}});var Vl=S((W_,Hl)=>{"use strict";var Vd=require("util"),ql=ss(),$d=cs(),Gd=Hl.exports=function(t){ql.call(this);let e=[],i=this;this._filter=new $d(t,{read:this.read.bind(this),write:function(r){e.push(r)},complete:function(){i.emit("complete",Buffer.concat(e))}}),this._filter.start()};Vd.inherits(Gd,ql)});var qi=S((Y_,$l)=>{"use strict";$l.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}});var hs=S((K_,Gl)=>{"use strict";var us=[];(function(){for(let t=0;t<256;t++){let e=t;for(let i=0;i<8;i++)e&1?e=3988292384^e>>>1:e=e>>>1;us[t]=e}})();var fs=Gl.exports=function(){this._crc=-1};fs.prototype.write=function(t){for(let e=0;e<t.length;e++)this._crc=us[(this._crc^t[e])&255]^this._crc>>>8;return!0};fs.prototype.crc32=function(){return this._crc^-1};fs.crc32=function(t){let e=-1;for(let i=0;i<t.length;i++)e=us[(e^t[i])&255]^e>>>8;return e^-1}});var ps=S((Z_,zl)=>{"use strict";var De=qi(),zd=hs(),Ve=zl.exports=function(t,e){this._options=t,t.checkCRC=t.checkCRC!==!1,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[De.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[De.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[De.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[De.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[De.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[De.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};Ve.prototype.start=function(){this.read(De.PNG_SIGNATURE.length,this._parseSignature.bind(this))};Ve.prototype._parseSignature=function(t){let e=De.PNG_SIGNATURE;for(let i=0;i<e.length;i++)if(t[i]!==e[i]){this.error(new Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))};Ve.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),i=t.readUInt32BE(4),r="";for(let s=4;s<8;s++)r+=String.fromCharCode(t[s]);let n=!!(t[4]&32);if(!this._hasIHDR&&i!==De.TYPE_IHDR){this.error(new Error("Expected IHDR on beggining"));return}if(this._crc=new zd,this._crc.write(Buffer.from(r)),this._chunks[i])return this._chunks[i](e);if(!n){this.error(new Error("Unsupported critical chunk type "+r));return}this.read(e+4,this._skipChunk.bind(this))};Ve.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))};Ve.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))};Ve.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),i=this._crc.crc32();if(this._options.checkCRC&&i!==e){this.error(new Error("Crc error - "+e+" - "+i));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))};Ve.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))};Ve.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),i=t.readUInt32BE(4),r=t[8],n=t[9],s=t[10],o=t[11],f=t[12];if(r!==8&&r!==4&&r!==2&&r!==1&&r!==16){this.error(new Error("Unsupported bit depth "+r));return}if(!(n in De.COLORTYPE_TO_BPP_MAP)){this.error(new Error("Unsupported color type"));return}if(s!==0){this.error(new Error("Unsupported compression method"));return}if(o!==0){this.error(new Error("Unsupported filter method"));return}if(f!==0&&f!==1){this.error(new Error("Unsupported interlace method"));return}this._colorType=n;let u=De.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:i,depth:r,interlace:!!f,palette:!!(n&De.COLORTYPE_PALETTE),color:!!(n&De.COLORTYPE_COLOR),alpha:!!(n&De.COLORTYPE_ALPHA),bpp:u,colorType:n}),this._handleChunkEnd()};Ve.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))};Ve.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let i=0;i<e;i++)this._palette.push([t[i*3],t[i*3+1],t[i*3+2],255]);this.palette(this._palette),this._handleChunkEnd()};Ve.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))};Ve.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===De.COLORTYPE_PALETTE_COLOR){if(this._palette.length===0){this.error(new Error("Transparency chunk must be after palette"));return}if(t.length>this._palette.length){this.error(new Error("More transparent colors than palette size"));return}for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===De.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===De.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()};Ve.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))};Ve.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/De.GAMMA_DIVISION),this._handleChunkEnd()};Ve.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))};Ve.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===De.COLORTYPE_PALETTE_COLOR&&this._palette.length===0)throw new Error("Expected palette not found");this.inflateData(e);let i=t-e.length;i>0?this._handleIDAT(i):this._handleChunkEnd()};Ve.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))};Ve.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}});var ds=S(Yl=>{"use strict";var Wl=as(),Wd=[function(){},function(t,e,i,r){if(r===e.length)throw new Error("Ran out of data");let n=e[r];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=255},function(t,e,i,r){if(r+1>=e.length)throw new Error("Ran out of data");let n=e[r];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=e[r+1]},function(t,e,i,r){if(r+2>=e.length)throw new Error("Ran out of data");t[i]=e[r],t[i+1]=e[r+1],t[i+2]=e[r+2],t[i+3]=255},function(t,e,i,r){if(r+3>=e.length)throw new Error("Ran out of data");t[i]=e[r],t[i+1]=e[r+1],t[i+2]=e[r+2],t[i+3]=e[r+3]}],Yd=[function(){},function(t,e,i,r){let n=e[0];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=r},function(t,e,i){let r=e[0];t[i]=r,t[i+1]=r,t[i+2]=r,t[i+3]=e[1]},function(t,e,i,r){t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=r},function(t,e,i){t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=e[3]}];function Kd(t,e){let i=[],r=0;function n(){if(r===t.length)throw new Error("Ran out of data");let s=t[r];r++;let o,f,u,h,l,d,m,_;switch(e){default:throw new Error("unrecognised depth");case 16:m=t[r],r++,i.push((s<<8)+m);break;case 4:m=s&15,_=s>>4,i.push(_,m);break;case 2:l=s&3,d=s>>2&3,m=s>>4&3,_=s>>6&3,i.push(_,m,d,l);break;case 1:o=s&1,f=s>>1&1,u=s>>2&1,h=s>>3&1,l=s>>4&1,d=s>>5&1,m=s>>6&1,_=s>>7&1,i.push(_,m,d,l,h,u,f,o);break}}return{get:function(s){for(;i.length<s;)n();let o=i.slice(0,s);return i=i.slice(s),o},resetAfterLine:function(){i.length=0},end:function(){if(r!==t.length)throw new Error("extra data found")}}}function Zd(t,e,i,r,n,s){let o=t.width,f=t.height,u=t.index;for(let h=0;h<f;h++)for(let l=0;l<o;l++){let d=i(l,h,u);Wd[r](e,n,d,s),s+=r}return s}function Xd(t,e,i,r,n,s){let o=t.width,f=t.height,u=t.index;for(let h=0;h<f;h++){for(let l=0;l<o;l++){let d=n.get(r),m=i(l,h,u);Yd[r](e,d,m,s)}n.resetAfterLine()}}Yl.dataToBitMap=function(t,e){let i=e.width,r=e.height,n=e.depth,s=e.bpp,o=e.interlace,f;n!==8&&(f=Kd(t,n));let u;n<=8?u=Buffer.alloc(i*r*4):u=new Uint16Array(i*r*4);let h=Math.pow(2,n)-1,l=0,d,m;if(o)d=Wl.getImagePasses(i,r),m=Wl.getInterlaceIterator(i,r);else{let _=0;m=function(){let v=_;return _+=4,v},d=[{width:i,height:r}]}for(let _=0;_<d.length;_++)n===8?l=Zd(d[_],u,m,s,t,l):Xd(d[_],u,m,s,f,h);if(n===8){if(l!==t.length)throw new Error("extra data found")}else f.end();return u}});var ms=S((J_,Kl)=>{"use strict";function Jd(t,e,i,r,n){let s=0;for(let o=0;o<r;o++)for(let f=0;f<i;f++){let u=n[t[s]];if(!u)throw new Error("index "+t[s]+" not in palette");for(let h=0;h<4;h++)e[s+h]=u[h];s+=4}}function Qd(t,e,i,r,n){let s=0;for(let o=0;o<r;o++)for(let f=0;f<i;f++){let u=!1;if(n.length===1?n[0]===t[s]&&(u=!0):n[0]===t[s]&&n[1]===t[s+1]&&n[2]===t[s+2]&&(u=!0),u)for(let h=0;h<4;h++)e[s+h]=0;s+=4}}function em(t,e,i,r,n){let s=255,o=Math.pow(2,n)-1,f=0;for(let u=0;u<r;u++)for(let h=0;h<i;h++){for(let l=0;l<4;l++)e[f+l]=Math.floor(t[f+l]*s/o+.5);f+=4}}Kl.exports=function(t,e,i=!1){let r=e.depth,n=e.width,s=e.height,o=e.colorType,f=e.transColor,u=e.palette,h=t;return o===3?Jd(t,h,n,s,u):(f&&Qd(t,h,n,s,f),r!==8&&!i&&(r===16&&(h=Buffer.alloc(n*s*4)),em(t,h,n,s,r))),h}});var Jl=S((Q_,Xl)=>{"use strict";var tm=require("util"),gs=require("zlib"),Zl=ss(),im=Vl(),rm=ps(),nm=ds(),sm=ms(),Bt=Xl.exports=function(t){Zl.call(this),this._parser=new rm(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};tm.inherits(Bt,Zl);Bt.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0};Bt.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=gs.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let i=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,r=Math.max(i,gs.Z_MIN_CHUNK);this._inflate=gs.createInflate({chunkSize:r});let n=i,s=this.emit.bind(this,"error");this._inflate.on("error",function(f){n&&s(f)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(f){n&&(f.length>n&&(f=f.slice(0,n)),n-=f.length,o(f))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)};Bt.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new im(this._bitmapInfo)};Bt.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t};Bt.prototype._handlePalette=function(t){this._bitmapInfo.palette=t};Bt.prototype._simpleTransparency=function(){this._metaData.alpha=!0};Bt.prototype._headersFinished=function(){this.emit("metadata",this._metaData)};Bt.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))};Bt.prototype._complete=function(t){if(this.errord)return;let e;try{let i=nm.dataToBitMap(t,this._bitmapInfo);e=sm(i,this._bitmapInfo,this._options.skipRescale),i=null}catch(i){this._handleError(i);return}this.emit("parsed",e)}});var ec=S((ex,Ql)=>{"use strict";var vt=qi();Ql.exports=function(t,e,i,r){let n=[vt.COLORTYPE_COLOR_ALPHA,vt.COLORTYPE_ALPHA].indexOf(r.colorType)!==-1;if(r.colorType===r.inputColorType){let v=function(){let x=new ArrayBuffer(2);return new DataView(x).setInt16(0,256,!0),new Int16Array(x)[0]!==256}();if(r.bitDepth===8||r.bitDepth===16&&v)return t}let s=r.bitDepth!==16?t:new Uint16Array(t.buffer),o=255,f=vt.COLORTYPE_TO_BPP_MAP[r.inputColorType];f===4&&!r.inputHasAlpha&&(f=3);let u=vt.COLORTYPE_TO_BPP_MAP[r.colorType];r.bitDepth===16&&(o=65535,u*=2);let h=Buffer.alloc(e*i*u),l=0,d=0,m=r.bgColor||{};m.red===void 0&&(m.red=o),m.green===void 0&&(m.green=o),m.blue===void 0&&(m.blue=o);function _(){let v,x,b,T=o;switch(r.inputColorType){case vt.COLORTYPE_COLOR_ALPHA:T=s[l+3],v=s[l],x=s[l+1],b=s[l+2];break;case vt.COLORTYPE_COLOR:v=s[l],x=s[l+1],b=s[l+2];break;case vt.COLORTYPE_ALPHA:T=s[l+1],v=s[l],x=v,b=v;break;case vt.COLORTYPE_GRAYSCALE:v=s[l],x=v,b=v;break;default:throw new Error("input color type:"+r.inputColorType+" is not supported at present")}return r.inputHasAlpha&&(n||(T/=o,v=Math.min(Math.max(Math.round((1-T)*m.red+T*v),0),o),x=Math.min(Math.max(Math.round((1-T)*m.green+T*x),0),o),b=Math.min(Math.max(Math.round((1-T)*m.blue+T*b),0),o))),{red:v,green:x,blue:b,alpha:T}}for(let v=0;v<i;v++)for(let x=0;x<e;x++){let b=_(s,l);switch(r.colorType){case vt.COLORTYPE_COLOR_ALPHA:case vt.COLORTYPE_COLOR:r.bitDepth===8?(h[d]=b.red,h[d+1]=b.green,h[d+2]=b.blue,n&&(h[d+3]=b.alpha)):(h.writeUInt16BE(b.red,d),h.writeUInt16BE(b.green,d+2),h.writeUInt16BE(b.blue,d+4),n&&h.writeUInt16BE(b.alpha,d+6));break;case vt.COLORTYPE_ALPHA:case vt.COLORTYPE_GRAYSCALE:{let T=(b.red+b.green+b.blue)/3;r.bitDepth===8?(h[d]=T,n&&(h[d+1]=b.alpha)):(h.writeUInt16BE(T,d),n&&h.writeUInt16BE(b.alpha,d+2));break}default:throw new Error("unrecognised color Type "+r.colorType)}l+=f,d+=u}return h}});var rc=S((tx,ic)=>{"use strict";var tc=ls();function om(t,e,i,r,n){for(let s=0;s<i;s++)r[n+s]=t[e+s]}function am(t,e,i){let r=0,n=e+i;for(let s=e;s<n;s++)r+=Math.abs(t[s]);return r}function lm(t,e,i,r,n,s){for(let o=0;o<i;o++){let f=o>=s?t[e+o-s]:0,u=t[e+o]-f;r[n+o]=u}}function cm(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,f=t[e+s]-o;n+=Math.abs(f)}return n}function um(t,e,i,r,n){for(let s=0;s<i;s++){let o=e>0?t[e+s-i]:0,f=t[e+s]-o;r[n+s]=f}}function fm(t,e,i){let r=0,n=e+i;for(let s=e;s<n;s++){let o=e>0?t[s-i]:0,f=t[s]-o;r+=Math.abs(f)}return r}function hm(t,e,i,r,n,s){for(let o=0;o<i;o++){let f=o>=s?t[e+o-s]:0,u=e>0?t[e+o-i]:0,h=t[e+o]-(f+u>>1);r[n+o]=h}}function pm(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,f=e>0?t[e+s-i]:0,u=t[e+s]-(o+f>>1);n+=Math.abs(u)}return n}function dm(t,e,i,r,n,s){for(let o=0;o<i;o++){let f=o>=s?t[e+o-s]:0,u=e>0?t[e+o-i]:0,h=e>0&&o>=s?t[e+o-(i+s)]:0,l=t[e+o]-tc(f,u,h);r[n+o]=l}}function mm(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,f=e>0?t[e+s-i]:0,u=e>0&&s>=r?t[e+s-(i+r)]:0,h=t[e+s]-tc(o,f,u);n+=Math.abs(h)}return n}var gm={0:om,1:lm,2:um,3:hm,4:dm},vm={0:am,1:cm,2:fm,3:pm,4:mm};ic.exports=function(t,e,i,r,n){let s;if(!("filterType"in r)||r.filterType===-1)s=[0,1,2,3,4];else if(typeof r.filterType=="number")s=[r.filterType];else throw new Error("unrecognised filter types");r.bitDepth===16&&(n*=2);let o=e*n,f=0,u=0,h=Buffer.alloc((o+1)*i),l=s[0];for(let d=0;d<i;d++){if(s.length>1){let m=1/0;for(let _=0;_<s.length;_++){let v=vm[s[_]](t,u,o,n);v<m&&(l=s[_],m=v)}}h[f]=l,f++,gm[l](t,u,o,h,f,n),f+=o,u+=o}return h}});var vs=S((ix,nc)=>{"use strict";var Xe=qi(),_m=hs(),xm=ec(),ym=rc(),bm=require("zlib"),Zt=nc.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32*1024,t.deflateLevel=t.deflateLevel!=null?t.deflateLevel:9,t.deflateStrategy=t.deflateStrategy!=null?t.deflateStrategy:3,t.inputHasAlpha=t.inputHasAlpha!=null?t.inputHasAlpha:!0,t.deflateFactory=t.deflateFactory||bm.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType=typeof t.colorType=="number"?t.colorType:Xe.COLORTYPE_COLOR_ALPHA,t.inputColorType=typeof t.inputColorType=="number"?t.inputColorType:Xe.COLORTYPE_COLOR_ALPHA,[Xe.COLORTYPE_GRAYSCALE,Xe.COLORTYPE_COLOR,Xe.COLORTYPE_COLOR_ALPHA,Xe.COLORTYPE_ALPHA].indexOf(t.colorType)===-1)throw new Error("option color type:"+t.colorType+" is not supported at present");if([Xe.COLORTYPE_GRAYSCALE,Xe.COLORTYPE_COLOR,Xe.COLORTYPE_COLOR_ALPHA,Xe.COLORTYPE_ALPHA].indexOf(t.inputColorType)===-1)throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(t.bitDepth!==8&&t.bitDepth!==16)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};Zt.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}};Zt.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())};Zt.prototype.filterData=function(t,e,i){let r=xm(t,e,i,this._options),n=Xe.COLORTYPE_TO_BPP_MAP[this._options.colorType];return ym(r,e,i,this._options,n)};Zt.prototype._packChunk=function(t,e){let i=e?e.length:0,r=Buffer.alloc(i+12);return r.writeUInt32BE(i,0),r.writeUInt32BE(t,4),e&&e.copy(r,8),r.writeInt32BE(_m.crc32(r.slice(4,r.length-4)),r.length-4),r};Zt.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*Xe.GAMMA_DIVISION),0),this._packChunk(Xe.TYPE_gAMA,e)};Zt.prototype.packIHDR=function(t,e){let i=Buffer.alloc(13);return i.writeUInt32BE(t,0),i.writeUInt32BE(e,4),i[8]=this._options.bitDepth,i[9]=this._options.colorType,i[10]=0,i[11]=0,i[12]=0,this._packChunk(Xe.TYPE_IHDR,i)};Zt.prototype.packIDAT=function(t){return this._packChunk(Xe.TYPE_IDAT,t)};Zt.prototype.packIEND=function(){return this._packChunk(Xe.TYPE_IEND,null)}});var lc=S((rx,ac)=>{"use strict";var wm=require("util"),sc=require("stream"),Sm=qi(),Em=vs(),oc=ac.exports=function(t){sc.call(this);let e=t||{};this._packer=new Em(e),this._deflate=this._packer.createDeflate(),this.readable=!0};wm.inherits(oc,sc);oc.prototype.pack=function(t,e,i,r){this.emit("data",Buffer.from(Sm.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,i)),r&&this.emit("data",this._packer.packGAMA(r));let n=this._packer.filterData(t,e,i);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(s){this.emit("data",this._packer.packIDAT(s))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(n)}});var dc=S((ur,pc)=>{"use strict";var cc=require("assert").ok,Hi=require("zlib"),km=require("util"),uc=require("buffer").kMaxLength;function di(t){if(!(this instanceof di))return new di(t);t&&t.chunkSize<Hi.Z_MIN_CHUNK&&(t.chunkSize=Hi.Z_MIN_CHUNK),Hi.Inflate.call(this,t),this._offset=this._offset===void 0?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&t.maxLength!=null&&(this._maxLength=t.maxLength)}function Cm(t){return new di(t)}function fc(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}di.prototype._processChunk=function(t,e,i){if(typeof i=="function")return Hi.Inflate._processChunk.call(this,t,e,i);let r=this,n=t&&t.length,s=this._chunkSize-this._offset,o=this._maxLength,f=0,u=[],h=0,l;this.on("error",function(v){l=v});function d(v,x){if(r._hadError)return;let b=s-x;if(cc(b>=0,"have should not go down"),b>0){let T=r._buffer.slice(r._offset,r._offset+b);if(r._offset+=b,T.length>o&&(T=T.slice(0,o)),u.push(T),h+=T.length,o-=T.length,o===0)return!1}return(x===0||r._offset>=r._chunkSize)&&(s=r._chunkSize,r._offset=0,r._buffer=Buffer.allocUnsafe(r._chunkSize)),x===0?(f+=n-v,n=v,!0):!1}cc(this._handle,"zlib binding closed");let m;do m=this._handle.writeSync(e,t,f,n,this._buffer,this._offset,s),m=m||this._writeState;while(!this._hadError&&d(m[0],m[1]));if(this._hadError)throw l;if(h>=uc)throw fc(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+uc.toString(16)+" bytes");let _=Buffer.concat(u,h);return fc(this),_};km.inherits(di,Hi.Inflate);function Om(t,e){if(typeof e=="string"&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");let i=t._finishFlushFlag;return i==null&&(i=Hi.Z_FINISH),t._processChunk(e,i)}function hc(t,e){return Om(new di(e),t)}pc.exports=ur=hc;ur.Inflate=di;ur.createInflate=Cm;ur.inflateSync=hc});var _s=S((nx,gc)=>{"use strict";var mc=gc.exports=function(t){this._buffer=t,this._reads=[]};mc.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})};mc.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}if(this._reads.length>0)throw new Error("There are some read requests waitng on finished stream");if(this._buffer.length>0)throw new Error("unrecognised content at end of stream")}});var _c=S(vc=>{"use strict";var Tm=_s(),Im=cs();vc.process=function(t,e){let i=[],r=new Tm(t);return new Im(e,{read:r.read.bind(r),write:function(s){i.push(s)},complete:function(){}}).start(),r.process(),Buffer.concat(i)}});var wc=S((ox,bc)=>{"use strict";var xc=!0,yc=require("zlib"),Am=dc();yc.deflateSync||(xc=!1);var Bm=_s(),Rm=_c(),Pm=ps(),Lm=ds(),Nm=ms();bc.exports=function(t,e){if(!xc)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let i;function r(R){i=R}let n;function s(R){n=R}function o(R){n.transColor=R}function f(R){n.palette=R}function u(){n.alpha=!0}let h;function l(R){h=R}let d=[];function m(R){d.push(R)}let _=new Bm(t);if(new Pm(e,{read:_.read.bind(_),error:r,metadata:s,gamma:l,palette:f,transColor:o,inflateData:m,simpleTransparency:u}).start(),_.process(),i)throw i;let x=Buffer.concat(d);d.length=0;let b;if(n.interlace)b=yc.inflateSync(x);else{let k=((n.width*n.bpp*n.depth+7>>3)+1)*n.height;b=Am(x,{chunkSize:k,maxLength:k})}if(x=null,!b||!b.length)throw new Error("bad png - invalid inflate data response");let T=Rm.process(b,n);x=null;let B=Lm.dataToBitMap(T,n);T=null;let E=Nm(B,n,e.skipRescale);return n.data=E,n.gamma=h||0,n}});var Cc=S((ax,kc)=>{"use strict";var Sc=!0,Ec=require("zlib");Ec.deflateSync||(Sc=!1);var Fm=qi(),Mm=vs();kc.exports=function(t,e){if(!Sc)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let i=e||{},r=new Mm(i),n=[];n.push(Buffer.from(Fm.PNG_SIGNATURE)),n.push(r.packIHDR(t.width,t.height)),t.gamma&&n.push(r.packGAMA(t.gamma));let s=r.filterData(t.data,t.width,t.height),o=Ec.deflateSync(s,r.getDeflateOptions());if(s=null,!o||!o.length)throw new Error("bad png - invalid compressed data response");return n.push(r.packIDAT(o)),n.push(r.packIEND()),Buffer.concat(n)}});var Oc=S(xs=>{"use strict";var Dm=wc(),Um=Cc();xs.read=function(t,e){return Dm(t,e||{})};xs.write=function(t,e){return Um(t,e)}});var Ac=S(Ic=>{"use strict";var jm=require("util"),Tc=require("stream"),qm=Jl(),Hm=lc(),Vm=Oc(),it=Ic.PNG=function(t){Tc.call(this),t=t||{},this.width=t.width|0,this.height=t.height|0,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new qm(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(e){this.data=e,this.emit("parsed",e)}.bind(this)),this._packer=new Hm(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};jm.inherits(it,Tc);it.sync=Vm;it.prototype.pack=function(){return!this.data||!this.data.length?(this.emit("error","No data provided"),this):(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this)};it.prototype.parse=function(t,e){if(e){let i,r;i=function(n){this.removeListener("error",r),this.data=n,e(null,this)}.bind(this),r=function(n){this.removeListener("parsed",i),e(n,null)}.bind(this),this.once("parsed",i),this.once("error",r)}return this.end(t),this};it.prototype.write=function(t){return this._parser.write(t),!0};it.prototype.end=function(t){this._parser.end(t)};it.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)};it.prototype._gamma=function(t){this.gamma=t};it.prototype._handleClose=function(){!this._parser.writable&&!this._packer.readable&&this.emit("close")};it.bitblt=function(t,e,i,r,n,s,o,f){if(i|=0,r|=0,n|=0,s|=0,o|=0,f|=0,i>t.width||r>t.height||i+n>t.width||r+s>t.height)throw new Error("bitblt reading outside image");if(o>e.width||f>e.height||o+n>e.width||f+s>e.height)throw new Error("bitblt writing outside image");for(let u=0;u<s;u++)t.data.copy(e.data,(f+u)*e.width+o<<2,(r+u)*t.width+i<<2,(r+u)*t.width+i+n<<2)};it.prototype.bitblt=function(t,e,i,r,n,s,o){return it.bitblt(this,t,e,i,r,n,s,o),this};it.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let i=0;i<t.width;i++){let r=t.width*e+i<<2;for(let n=0;n<3;n++){let s=t.data[r+n]/255;s=Math.pow(s,1/2.2/t.gamma),t.data[r+n]=Math.round(s*255)}}t.gamma=0}};it.prototype.adjustGamma=function(){it.adjustGamma(this)}});var fr=S(bs=>{var Jr=class extends Error{constructor(e,i,r){super(r),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=i,this.exitCode=e,this.nestedError=void 0}},ys=class extends Jr{constructor(e){super(1,"commander.invalidArgument",e),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}};bs.CommanderError=Jr;bs.InvalidArgumentError=ys});var Qr=S(Ss=>{var{InvalidArgumentError:$m}=fr(),ws=class{constructor(e,i){switch(this.description=i||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,e[0]){case"<":this.required=!0,this._name=e.slice(1,-1);break;case"[":this.required=!1,this._name=e.slice(1,-1);break;default:this.required=!0,this._name=e;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(e,i){return i===this.defaultValue||!Array.isArray(i)?[e]:i.concat(e)}default(e,i){return this.defaultValue=e,this.defaultValueDescription=i,this}argParser(e){return this.parseArg=e,this}choices(e){return this.argChoices=e,this.parseArg=(i,r)=>{if(!e.includes(i))throw new $m(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(i,r):i},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}};function Gm(t){let e=t.name()+(t.variadic===!0?"...":"");return t.required?"<"+e+">":"["+e+"]"}Ss.Argument=ws;Ss.humanReadableArgName=Gm});var ks=S(Bc=>{var{humanReadableArgName:zm}=Qr(),Es=class{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1}visibleCommands(e){let i=e.commands.filter(r=>!r._hidden);if(e._hasImplicitHelpCommand()){let[,r,n]=e._helpCommandnameAndArgs.match(/([^ ]+) *(.*)/),s=e.createCommand(r).helpOption(!1);s.description(e._helpCommandDescription),n&&s.arguments(n),i.push(s)}return this.sortSubcommands&&i.sort((r,n)=>r.name().localeCompare(n.name())),i}visibleOptions(e){let i=e.options.filter(s=>!s.hidden),r=e._hasHelpOption&&e._helpShortFlag&&!e._findOption(e._helpShortFlag),n=e._hasHelpOption&&!e._findOption(e._helpLongFlag);if(r||n){let s;r?n?s=e.createOption(e._helpFlags,e._helpDescription):s=e.createOption(e._helpShortFlag,e._helpDescription):s=e.createOption(e._helpLongFlag,e._helpDescription),i.push(s)}if(this.sortOptions){let s=o=>o.short?o.short.replace(/^-/,""):o.long.replace(/^--/,"");i.sort((o,f)=>s(o).localeCompare(s(f)))}return i}visibleArguments(e){return e._argsDescription&&e._args.forEach(i=>{i.description=i.description||e._argsDescription[i.name()]||""}),e._args.find(i=>i.description)?e._args:[]}subcommandTerm(e){let i=e._args.map(r=>zm(r)).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(i?" "+i:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,i){return i.visibleCommands(e).reduce((r,n)=>Math.max(r,i.subcommandTerm(n).length),0)}longestOptionTermLength(e,i){return i.visibleOptions(e).reduce((r,n)=>Math.max(r,i.optionTerm(n).length),0)}longestArgumentTermLength(e,i){return i.visibleArguments(e).reduce((r,n)=>Math.max(r,i.argumentTerm(n).length),0)}commandUsage(e){let i=e._name;e._aliases[0]&&(i=i+"|"+e._aliases[0]);let r="";for(let n=e.parent;n;n=n.parent)r=n.name()+" "+r;return r+i+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.description()}optionDescription(e){let i=[];return e.argChoices&&!e.negate&&i.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&!e.negate&&i.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),e.envVar!==void 0&&i.push(`env: ${e.envVar}`),i.length>0?`${e.description} (${i.join(", ")})`:e.description}argumentDescription(e){let i=[];if(e.argChoices&&i.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&i.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),i.length>0){let r=`(${i.join(", ")})`;return e.description?`${e.description} ${r}`:r}return e.description}formatHelp(e,i){let r=i.padWidth(e,i),n=i.helpWidth||80,s=2,o=2;function f(v,x){if(x){let b=`${v.padEnd(r+o)}${x}`;return i.wrap(b,n-s,r+o)}return v}function u(v){return v.join(`
`).replace(/^/gm," ".repeat(s))}let h=[`Usage: ${i.commandUsage(e)}`,""],l=i.commandDescription(e);l.length>0&&(h=h.concat([l,""]));let d=i.visibleArguments(e).map(v=>f(i.argumentTerm(v),i.argumentDescription(v)));d.length>0&&(h=h.concat(["Arguments:",u(d),""]));let m=i.visibleOptions(e).map(v=>f(i.optionTerm(v),i.optionDescription(v)));m.length>0&&(h=h.concat(["Options:",u(m),""]));let _=i.visibleCommands(e).map(v=>f(i.subcommandTerm(v),i.subcommandDescription(v)));return _.length>0&&(h=h.concat(["Commands:",u(_),""])),h.join(`
`)}padWidth(e,i){return Math.max(i.longestOptionTermLength(e,i),i.longestSubcommandTermLength(e,i),i.longestArgumentTermLength(e,i))}wrap(e,i,r,n=40){if(e.match(/[\n]\s+/))return e;let s=i-r;if(s<n)return e;let o=e.substr(0,r),f=e.substr(r),u=" ".repeat(r),h=new RegExp(".{1,"+(s-1)+"}([\\s\u200B]|$)|[^\\s\u200B]+?([\\s\u200B]|$)","g"),l=f.match(h)||[];return o+l.map((d,m)=>(d.slice(-1)===`
`&&(d=d.slice(0,d.length-1)),(m>0?u:"")+d.trimRight())).join(`
`)}};Bc.Help=Es});var Ts=S(Os=>{var{InvalidArgumentError:Wm}=fr(),Cs=class{constructor(e,i){this.flags=e,this.description=i||"",this.required=e.includes("<"),this.optional=e.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(e),this.mandatory=!1;let r=Rc(e);this.short=r.shortFlag,this.long=r.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0}default(e,i){return this.defaultValue=e,this.defaultValueDescription=i,this}env(e){return this.envVar=e,this}argParser(e){return this.parseArg=e,this}makeOptionMandatory(e=!0){return this.mandatory=!!e,this}hideHelp(e=!0){return this.hidden=!!e,this}_concatValue(e,i){return i===this.defaultValue||!Array.isArray(i)?[e]:i.concat(e)}choices(e){return this.argChoices=e,this.parseArg=(i,r)=>{if(!e.includes(i))throw new Wm(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(i,r):i},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return Ym(this.name().replace(/^no-/,""))}is(e){return this.short===e||this.long===e}};function Ym(t){return t.split("-").reduce((e,i)=>e+i[0].toUpperCase()+i.slice(1))}function Rc(t){let e,i,r=t.split(/[ |,]+/);return r.length>1&&!/^[[<]/.test(r[1])&&(e=r.shift()),i=r.shift(),!e&&/^-[^-]$/.test(i)&&(e=i,i=void 0),{shortFlag:e,longFlag:i}}Os.Option=Cs;Os.splitOptionFlags=Rc});var Lc=S(Pc=>{function Km(t,e){if(Math.abs(t.length-e.length)>3)return Math.max(t.length,e.length);let i=[];for(let r=0;r<=t.length;r++)i[r]=[r];for(let r=0;r<=e.length;r++)i[0][r]=r;for(let r=1;r<=e.length;r++)for(let n=1;n<=t.length;n++){let s=1;t[n-1]===e[r-1]?s=0:s=1,i[n][r]=Math.min(i[n-1][r]+1,i[n][r-1]+1,i[n-1][r-1]+s),n>1&&r>1&&t[n-1]===e[r-2]&&t[n-2]===e[r-1]&&(i[n][r]=Math.min(i[n][r],i[n-2][r-2]+1))}return i[t.length][e.length]}function Zm(t,e){if(!e||e.length===0)return"";e=Array.from(new Set(e));let i=t.startsWith("--");i&&(t=t.slice(2),e=e.map(o=>o.slice(2)));let r=[],n=3,s=.4;return e.forEach(o=>{if(o.length<=1)return;let f=Km(t,o),u=Math.max(t.length,o.length);(u-f)/u>s&&(f<n?(n=f,r=[o]):f===n&&r.push(o))}),r.sort((o,f)=>o.localeCompare(f)),i&&(r=r.map(o=>`--${o}`)),r.length>1?`
(Did you mean one of ${r.join(", ")}?)`:r.length===1?`
(Did you mean ${r[0]}?)`:""}Pc.suggestSimilar=Zm});var Uc=S(Dc=>{var Xm=require("events").EventEmitter,Is=require("child_process"),mi=require("path"),As=require("fs"),{Argument:Jm,humanReadableArgName:Qm}=Qr(),{CommanderError:Bs}=fr(),{Help:e0}=ks(),{Option:t0,splitOptionFlags:i0}=Ts(),{suggestSimilar:Nc}=Lc(),Ps=class t extends Xm{constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this._args=[],this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!1,this._outputConfiguration={writeOut:i=>process.stdout.write(i),writeErr:i=>process.stderr.write(i),getOutHelpWidth:()=>process.stdout.isTTY?process.stdout.columns:void 0,getErrHelpWidth:()=>process.stderr.isTTY?process.stderr.columns:void 0,outputError:(i,r)=>r(i)},this._hidden=!1,this._hasHelpOption=!0,this._helpFlags="-h, --help",this._helpDescription="display help for command",this._helpShortFlag="-h",this._helpLongFlag="--help",this._addImplicitHelpCommand=void 0,this._helpCommandName="help",this._helpCommandnameAndArgs="help [command]",this._helpCommandDescription="display help for command",this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._hasHelpOption=e._hasHelpOption,this._helpFlags=e._helpFlags,this._helpDescription=e._helpDescription,this._helpShortFlag=e._helpShortFlag,this._helpLongFlag=e._helpLongFlag,this._helpCommandName=e._helpCommandName,this._helpCommandnameAndArgs=e._helpCommandnameAndArgs,this._helpCommandDescription=e._helpCommandDescription,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}command(e,i,r){let n=i,s=r;typeof n=="object"&&n!==null&&(s=n,n=null),s=s||{};let[,o,f]=e.match(/([^ ]+) *(.*)/),u=this.createCommand(o);return n&&(u.description(n),u._executableHandler=!0),s.isDefault&&(this._defaultCommandName=u._name),u._hidden=!!(s.noHelp||s.hidden),u._executableFile=s.executableFile||null,f&&u.arguments(f),this.commands.push(u),u.parent=this,u.copyInheritedSettings(this),n?this:u}createCommand(e){return new t(e)}createHelp(){return Object.assign(new e0,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,i){if(!e._name)throw new Error("Command passed to .addCommand() must have a name");function r(n){n.forEach(s=>{if(s._executableHandler&&!s._executableFile)throw new Error(`Must specify executableFile for deeply nested executable: ${s.name()}`);r(s.commands)})}return r(e.commands),i=i||{},i.isDefault&&(this._defaultCommandName=e._name),(i.noHelp||i.hidden)&&(e._hidden=!0),this.commands.push(e),e.parent=this,this}createArgument(e,i){return new Jm(e,i)}argument(e,i,r,n){let s=this.createArgument(e,i);return typeof r=="function"?s.default(n).argParser(r):s.default(r),this.addArgument(s),this}arguments(e){return e.split(/ +/).forEach(i=>{this.argument(i)}),this}addArgument(e){let i=this._args.slice(-1)[0];if(i&&i.variadic)throw new Error(`only the last argument can be variadic '${i.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this._args.push(e),this}addHelpCommand(e,i){return e===!1?this._addImplicitHelpCommand=!1:(this._addImplicitHelpCommand=!0,typeof e=="string"&&(this._helpCommandName=e.split(" ")[0],this._helpCommandnameAndArgs=e),this._helpCommandDescription=i||this._helpCommandDescription),this}_hasImplicitHelpCommand(){return this._addImplicitHelpCommand===void 0?this.commands.length&&!this._actionHandler&&!this._findCommand("help"):this._addImplicitHelpCommand}hook(e,i){let r=["preAction","postAction"];if(!r.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${r.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(i):this._lifeCycleHooks[e]=[i],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=i=>{if(i.code!=="commander.executeSubCommandAsync")throw i},this}_exit(e,i,r){this._exitCallback&&this._exitCallback(new Bs(e,i,r)),process.exit(e)}action(e){let i=r=>{let n=this._args.length,s=r.slice(0,n);return this._storeOptionsAsProperties?s[n]=this:s[n]=this.opts(),s.push(this),e.apply(this,s)};return this._actionHandler=i,this}createOption(e,i){return new t0(e,i)}addOption(e){let i=e.name(),r=e.attributeName(),n=e.defaultValue;if(e.negate||e.optional||e.required||typeof n=="boolean"){if(e.negate){let o=e.long.replace(/^--no-/,"--");n=this._findOption(o)?this.getOptionValue(r):!0}n!==void 0&&this.setOptionValueWithSource(r,n,"default")}this.options.push(e);let s=(o,f,u)=>{let h=this.getOptionValue(r);if(o!==null&&e.parseArg)try{o=e.parseArg(o,h===void 0?n:h)}catch(l){if(l.code==="commander.invalidArgument"){let d=`${f} ${l.message}`;this._displayError(l.exitCode,l.code,d)}throw l}else o!==null&&e.variadic&&(o=e._concatValue(o,h));typeof h=="boolean"||typeof h=="undefined"?o==null?this.setOptionValueWithSource(r,e.negate?!1:n||!0,u):this.setOptionValueWithSource(r,o,u):o!==null&&this.setOptionValueWithSource(r,e.negate?!1:o,u)};return this.on("option:"+i,o=>{let f=`error: option '${e.flags}' argument '${o}' is invalid.`;s(o,f,"cli")}),e.envVar&&this.on("optionEnv:"+i,o=>{let f=`error: option '${e.flags}' value '${o}' from env '${e.envVar}' is invalid.`;s(o,f,"env")}),this}_optionEx(e,i,r,n,s){let o=this.createOption(i,r);if(o.makeOptionMandatory(!!e.mandatory),typeof n=="function")o.default(s).argParser(n);else if(n instanceof RegExp){let f=n;n=(u,h)=>{let l=f.exec(u);return l?l[0]:h},o.default(s).argParser(n)}else o.default(n);return this.addOption(o)}option(e,i,r,n){return this._optionEx({},e,i,r,n)}requiredOption(e,i,r,n){return this._optionEx({mandatory:!0},e,i,r,n)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){if(this._passThroughOptions=!!e,this.parent&&e&&!this.parent._enablePositionalOptions)throw new Error("passThroughOptions can not be used without turning on enablePositionalOptions for parent command(s)");return this}storeOptionsAsProperties(e=!0){if(this._storeOptionsAsProperties=!!e,this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");return this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,i){return this._storeOptionsAsProperties?this[e]=i:this._optionValues[e]=i,this}setOptionValueWithSource(e,i,r){return this.setOptionValue(e,i),this._optionValueSources[e]=r,this}getOptionValueSource(e){return this._optionValueSources[e]}_prepareUserArgs(e,i){if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");i=i||{},e===void 0&&(e=process.argv,process.versions&&process.versions.electron&&(i.from="electron")),this.rawArgs=e.slice();let r;switch(i.from){case void 0:case"node":this._scriptPath=e[1],r=e.slice(2);break;case"electron":process.defaultApp?(this._scriptPath=e[1],r=e.slice(2)):r=e.slice(1);break;case"user":r=e.slice(0);break;default:throw new Error(`unexpected parse option { from: '${i.from}' }`)}return!this._scriptPath&&require.main&&(this._scriptPath=require.main.filename),this._name=this._name||this._scriptPath&&mi.basename(this._scriptPath,mi.extname(this._scriptPath)),r}parse(e,i){let r=this._prepareUserArgs(e,i);return this._parseCommand([],r),this}async parseAsync(e,i){let r=this._prepareUserArgs(e,i);return await this._parseCommand([],r),this}_executeSubCommand(e,i){i=i.slice();let r=!1,n=[".js",".ts",".tsx",".mjs",".cjs"];this._checkForMissingMandatoryOptions();let s=this._scriptPath;!s&&require.main&&(s=require.main.filename);let o;try{let m=As.realpathSync(s);o=mi.dirname(m)}catch{o="."}let f=mi.basename(s,mi.extname(s))+"-"+e._name;e._executableFile&&(f=e._executableFile);let u=mi.join(o,f);As.existsSync(u)?f=u:n.forEach(m=>{As.existsSync(`${u}${m}`)&&(f=`${u}${m}`)}),r=n.includes(mi.extname(f));let h;process.platform!=="win32"?r?(i.unshift(f),i=Mc(process.execArgv).concat(i),h=Is.spawn(process.argv[0],i,{stdio:"inherit"})):h=Is.spawn(f,i,{stdio:"inherit"}):(i.unshift(f),i=Mc(process.execArgv).concat(i),h=Is.spawn(process.execPath,i,{stdio:"inherit"})),["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(m=>{process.on(m,()=>{h.killed===!1&&h.exitCode===null&&h.kill(m)})});let d=this._exitCallback;d?h.on("close",()=>{d(new Bs(process.exitCode||0,"commander.executeSubCommandAsync","(close)"))}):h.on("close",process.exit.bind(process)),h.on("error",m=>{if(m.code==="ENOENT"){let _=`'${f}' does not exist
 - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name`;throw new Error(_)}else if(m.code==="EACCES")throw new Error(`'${f}' not executable`);if(!d)process.exit(1);else{let _=new Bs(1,"commander.executeSubCommandAsync","(error)");_.nestedError=m,d(_)}}),this.runningCommand=h}_dispatchSubcommand(e,i,r){let n=this._findCommand(e);if(n||this.help({error:!0}),n._executableHandler)this._executeSubCommand(n,i.concat(r));else return n._parseCommand(i,r)}_checkNumberOfArguments(){this._args.forEach((e,i)=>{e.required&&this.args[i]==null&&this.missingArgument(e.name())}),!(this._args.length>0&&this._args[this._args.length-1].variadic)&&this.args.length>this._args.length&&this._excessArguments(this.args)}_processArguments(){let e=(r,n,s)=>{let o=n;if(n!==null&&r.parseArg)try{o=r.parseArg(n,s)}catch(f){if(f.code==="commander.invalidArgument"){let u=`error: command-argument value '${n}' is invalid for argument '${r.name()}'. ${f.message}`;this._displayError(f.exitCode,f.code,u)}throw f}return o};this._checkNumberOfArguments();let i=[];this._args.forEach((r,n)=>{let s=r.defaultValue;r.variadic?n<this.args.length?(s=this.args.slice(n),r.parseArg&&(s=s.reduce((o,f)=>e(r,f,o),r.defaultValue))):s===void 0&&(s=[]):n<this.args.length&&(s=this.args[n],r.parseArg&&(s=e(r,s,r.defaultValue))),i[n]=s}),this.processedArgs=i}_chainOrCall(e,i){return e&&e.then&&typeof e.then=="function"?e.then(()=>i()):i()}_chainOrCallHooks(e,i){let r=e,n=[];return Rs(this).reverse().filter(s=>s._lifeCycleHooks[i]!==void 0).forEach(s=>{s._lifeCycleHooks[i].forEach(o=>{n.push({hookedCommand:s,callback:o})})}),i==="postAction"&&n.reverse(),n.forEach(s=>{r=this._chainOrCall(r,()=>s.callback(s.hookedCommand,this))}),r}_parseCommand(e,i){let r=this.parseOptions(i);if(this._parseOptionsEnv(),e=e.concat(r.operands),i=r.unknown,this.args=e.concat(i),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),i);if(this._hasImplicitHelpCommand()&&e[0]===this._helpCommandName)return e.length===1&&this.help(),this._dispatchSubcommand(e[1],[],[this._helpLongFlag]);if(this._defaultCommandName)return Fc(this,i),this._dispatchSubcommand(this._defaultCommandName,e,i);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),Fc(this,r.unknown),this._checkForMissingMandatoryOptions();let n=()=>{r.unknown.length>0&&this.unknownOption(r.unknown[0])},s=`command:${this.name()}`;if(this._actionHandler){n(),this._processArguments();let o;return o=this._chainOrCallHooks(o,"preAction"),o=this._chainOrCall(o,()=>this._actionHandler(this.processedArgs)),this.parent&&this.parent.emit(s,e,i),o=this._chainOrCallHooks(o,"postAction"),o}if(this.parent&&this.parent.listenerCount(s))n(),this._processArguments(),this.parent.emit(s,e,i);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,i);this.listenerCount("command:*")?this.emit("command:*",e,i):this.commands.length?this.unknownCommand():(n(),this._processArguments())}else this.commands.length?(n(),this.help({error:!0})):(n(),this._processArguments())}_findCommand(e){if(e)return this.commands.find(i=>i._name===e||i._aliases.includes(e))}_findOption(e){return this.options.find(i=>i.is(e))}_checkForMissingMandatoryOptions(){for(let e=this;e;e=e.parent)e.options.forEach(i=>{i.mandatory&&e.getOptionValue(i.attributeName())===void 0&&e.missingMandatoryOptionValue(i)})}parseOptions(e){let i=[],r=[],n=i,s=e.slice();function o(u){return u.length>1&&u[0]==="-"}let f=null;for(;s.length;){let u=s.shift();if(u==="--"){n===r&&n.push(u),n.push(...s);break}if(f&&!o(u)){this.emit(`option:${f.name()}`,u);continue}if(f=null,o(u)){let h=this._findOption(u);if(h){if(h.required){let l=s.shift();l===void 0&&this.optionMissingArgument(h),this.emit(`option:${h.name()}`,l)}else if(h.optional){let l=null;s.length>0&&!o(s[0])&&(l=s.shift()),this.emit(`option:${h.name()}`,l)}else this.emit(`option:${h.name()}`);f=h.variadic?h:null;continue}}if(u.length>2&&u[0]==="-"&&u[1]!=="-"){let h=this._findOption(`-${u[1]}`);if(h){h.required||h.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${h.name()}`,u.slice(2)):(this.emit(`option:${h.name()}`),s.unshift(`-${u.slice(2)}`));continue}}if(/^--[^=]+=/.test(u)){let h=u.indexOf("="),l=this._findOption(u.slice(0,h));if(l&&(l.required||l.optional)){this.emit(`option:${l.name()}`,u.slice(h+1));continue}}if(o(u)&&(n=r),(this._enablePositionalOptions||this._passThroughOptions)&&i.length===0&&r.length===0){if(this._findCommand(u)){i.push(u),s.length>0&&r.push(...s);break}else if(u===this._helpCommandName&&this._hasImplicitHelpCommand()){i.push(u),s.length>0&&i.push(...s);break}else if(this._defaultCommandName){r.push(u),s.length>0&&r.push(...s);break}}if(this._passThroughOptions){n.push(u),s.length>0&&n.push(...s);break}n.push(u)}return{operands:i,unknown:r}}opts(){if(this._storeOptionsAsProperties){let e={},i=this.options.length;for(let r=0;r<i;r++){let n=this.options[r].attributeName();e[n]=n===this._versionOptionName?this._version:this[n]}return e}return this._optionValues}_displayError(e,i,r){this._outputConfiguration.outputError(`${r}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0})),this._exit(e,i,r)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in process.env){let i=e.attributeName();(this.getOptionValue(i)===void 0||["default","config","env"].includes(this.getOptionValueSource(i)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,process.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}missingArgument(e){let i=`error: missing required argument '${e}'`;this._displayError(1,"commander.missingArgument",i)}optionMissingArgument(e){let i=`error: option '${e.flags}' argument missing`;this._displayError(1,"commander.optionMissingArgument",i)}missingMandatoryOptionValue(e){let i=`error: required option '${e.flags}' not specified`;this._displayError(1,"commander.missingMandatoryOptionValue",i)}unknownOption(e){if(this._allowUnknownOption)return;let i="";if(e.startsWith("--")&&this._showSuggestionAfterError){let n=[],s=this;do{let o=s.createHelp().visibleOptions(s).filter(f=>f.long).map(f=>f.long);n=n.concat(o),s=s.parent}while(s&&!s._enablePositionalOptions);i=Nc(e,n)}let r=`error: unknown option '${e}'${i}`;this._displayError(1,"commander.unknownOption",r)}_excessArguments(e){if(this._allowExcessArguments)return;let i=this._args.length,r=i===1?"":"s",s=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${i} argument${r} but got ${e.length}.`;this._displayError(1,"commander.excessArguments",s)}unknownCommand(){let e=this.args[0],i="";if(this._showSuggestionAfterError){let n=[];this.createHelp().visibleCommands(this).forEach(s=>{n.push(s.name()),s.alias()&&n.push(s.alias())}),i=Nc(e,n)}let r=`error: unknown command '${e}'${i}`;this._displayError(1,"commander.unknownCommand",r)}version(e,i,r){if(e===void 0)return this._version;this._version=e,i=i||"-V, --version",r=r||"output the version number";let n=this.createOption(i,r);return this._versionOptionName=n.attributeName(),this.options.push(n),this.on("option:"+n.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,i){return e===void 0&&i===void 0?this._description:(this._description=e,i&&(this._argsDescription=i),this)}alias(e){if(e===void 0)return this._aliases[0];let i=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(i=this.commands[this.commands.length-1]),e===i._name)throw new Error("Command alias can't be the same as its name");return i._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(i=>this.alias(i)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;let i=this._args.map(r=>Qm(r));return[].concat(this.options.length||this._hasHelpOption?"[options]":[],this.commands.length?"[command]":[],this._args.length?i:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}helpInformation(e){let i=this.createHelp();return i.helpWidth===void 0&&(i.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()),i.formatHelp(this,i)}_getHelpContext(e){e=e||{};let i={error:!!e.error},r;return i.error?r=n=>this._outputConfiguration.writeErr(n):r=n=>this._outputConfiguration.writeOut(n),i.write=e.write||r,i.command=this,i}outputHelp(e){let i;typeof e=="function"&&(i=e,e=void 0);let r=this._getHelpContext(e);Rs(this).reverse().forEach(s=>s.emit("beforeAllHelp",r)),this.emit("beforeHelp",r);let n=this.helpInformation(r);if(i&&(n=i(n),typeof n!="string"&&!Buffer.isBuffer(n)))throw new Error("outputHelp callback must return a string or a Buffer");r.write(n),this.emit(this._helpLongFlag),this.emit("afterHelp",r),Rs(this).forEach(s=>s.emit("afterAllHelp",r))}helpOption(e,i){if(typeof e=="boolean")return this._hasHelpOption=e,this;this._helpFlags=e||this._helpFlags,this._helpDescription=i||this._helpDescription;let r=i0(this._helpFlags);return this._helpShortFlag=r.shortFlag,this._helpLongFlag=r.longFlag,this}help(e){this.outputHelp(e);let i=process.exitCode||0;i===0&&e&&typeof e!="function"&&e.error&&(i=1),this._exit(i,"commander.help","(outputHelp)")}addHelpText(e,i){let r=["beforeAll","before","after","afterAll"];if(!r.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${r.join("', '")}'`);let n=`${e}Help`;return this.on(n,s=>{let o;typeof i=="function"?o=i({error:s.error,command:s.command}):o=i,o&&s.write(`${o}
`)}),this}};function Fc(t,e){t._hasHelpOption&&e.find(r=>r===t._helpLongFlag||r===t._helpShortFlag)&&(t.outputHelp(),t._exit(0,"commander.helpDisplayed","(outputHelp)"))}function Mc(t){return t.map(e=>{if(!e.startsWith("--inspect"))return e;let i,r="127.0.0.1",n="9229",s;return(s=e.match(/^(--inspect(-brk)?)$/))!==null?i=s[1]:(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(i=s[1],/^\d+$/.test(s[3])?n=s[3]:r=s[3]):(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(i=s[1],r=s[3],n=s[4]),i&&n!=="0"?`${i}=${r}:${parseInt(n)+1}`:e})}function Rs(t){let e=[];for(let i=t;i;i=i.parent)e.push(i);return e}Dc.Command=Ps});var Vc=S((Ot,Hc)=>{var{Argument:r0}=Qr(),{Command:jc}=Uc(),{CommanderError:n0,InvalidArgumentError:qc}=fr(),{Help:s0}=ks(),{Option:o0}=Ts();Ot=Hc.exports=new jc;Ot.program=Ot;Ot.Argument=r0;Ot.Command=jc;Ot.CommanderError=n0;Ot.Help=s0;Ot.InvalidArgumentError=qc;Ot.InvalidOptionArgumentError=qc;Ot.Option=o0});var zc=S(($c,Gc)=>{$c=Gc.exports=Vi;function Vi(t,e){if(this.stream=e.stream||process.stderr,typeof e=="number"){var i=e;e={},e.total=i}else{if(e=e||{},typeof t!="string")throw new Error("format required");if(typeof e.total!="number")throw new Error("total required")}this.fmt=t,this.curr=e.curr||0,this.total=e.total,this.width=e.width||this.total,this.clear=e.clear,this.chars={complete:e.complete||"=",incomplete:e.incomplete||"-",head:e.head||e.complete||"="},this.renderThrottle=e.renderThrottle!==0?e.renderThrottle||16:0,this.lastRender=-1/0,this.callback=e.callback||function(){},this.tokens={},this.lastDraw=""}Vi.prototype.tick=function(t,e){if(t!==0&&(t=t||1),typeof t=="object"&&(e=t,t=1),e&&(this.tokens=e),this.curr==0&&(this.start=new Date),this.curr+=t,this.render(),this.curr>=this.total){this.render(void 0,!0),this.complete=!0,this.terminate(),this.callback(this);return}};Vi.prototype.render=function(t,e){if(e=e!==void 0?e:!1,t&&(this.tokens=t),!!this.stream.isTTY){var i=Date.now(),r=i-this.lastRender;if(!(!e&&r<this.renderThrottle)){this.lastRender=i;var n=this.curr/this.total;n=Math.min(Math.max(n,0),1);var s=Math.floor(n*100),o,f,u,h=new Date-this.start,l=s==100?0:h*(this.total/this.curr-1),d=this.curr/(h/1e3),m=this.fmt.replace(":current",this.curr).replace(":total",this.total).replace(":elapsed",isNaN(h)?"0.0":(h/1e3).toFixed(1)).replace(":eta",isNaN(l)||!isFinite(l)?"0.0":(l/1e3).toFixed(1)).replace(":percent",s.toFixed(0)+"%").replace(":rate",Math.round(d)),_=Math.max(0,this.stream.columns-m.replace(":bar","").length);_&&process.platform==="win32"&&(_=_-1);var v=Math.min(this.width,_);if(u=Math.round(v*n),f=Array(Math.max(0,u+1)).join(this.chars.complete),o=Array(Math.max(0,v-u+1)).join(this.chars.incomplete),u>0&&(f=f.slice(0,-1)+this.chars.head),m=m.replace(":bar",f+o),this.tokens)for(var x in this.tokens)m=m.replace(":"+x,this.tokens[x]);this.lastDraw!==m&&(this.stream.cursorTo(0),this.stream.write(m),this.stream.clearLine(1),this.lastDraw=m)}}};Vi.prototype.update=function(t,e){var i=Math.floor(t*this.total),r=i-this.curr;this.tick(r,e)};Vi.prototype.interrupt=function(t){this.stream.clearLine(),this.stream.cursorTo(0),this.stream.write(t),this.stream.write(`
`),this.stream.write(this.lastDraw)};Vi.prototype.terminate=function(){this.clear?this.stream.clearLine&&(this.stream.clearLine(),this.stream.cursorTo(0)):this.stream.write(`
`)}});var Yc=S((gx,Wc)=>{Wc.exports=zc()});var Jc=S(jt=>{"use strict";Object.defineProperty(jt,"__esModule",{value:!0});var Kc=require("buffer"),gi={INVALID_ENCODING:"Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.",INVALID_SMARTBUFFER_SIZE:"Invalid size provided. Size must be a valid integer greater than zero.",INVALID_SMARTBUFFER_BUFFER:"Invalid Buffer provided in SmartBufferOptions.",INVALID_SMARTBUFFER_OBJECT:"Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.",INVALID_OFFSET:"An invalid offset value was provided.",INVALID_OFFSET_NON_NUMBER:"An invalid offset value was provided. A numeric value is required.",INVALID_LENGTH:"An invalid length value was provided.",INVALID_LENGTH_NON_NUMBER:"An invalid length value was provived. A numeric value is required.",INVALID_TARGET_OFFSET:"Target offset is beyond the bounds of the internal SmartBuffer data.",INVALID_TARGET_LENGTH:"Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.",INVALID_READ_BEYOND_BOUNDS:"Attempted to read beyond the bounds of the managed data.",INVALID_WRITE_BEYOND_BOUNDS:"Attempted to write beyond the bounds of the managed data."};jt.ERRORS=gi;function a0(t){if(!Kc.Buffer.isEncoding(t))throw new Error(gi.INVALID_ENCODING)}jt.checkEncoding=a0;function Zc(t){return typeof t=="number"&&isFinite(t)&&f0(t)}jt.isFiniteInteger=Zc;function Xc(t,e){if(typeof t=="number"){if(!Zc(t)||t<0)throw new Error(e?gi.INVALID_OFFSET:gi.INVALID_LENGTH)}else throw new Error(e?gi.INVALID_OFFSET_NON_NUMBER:gi.INVALID_LENGTH_NON_NUMBER)}function l0(t){Xc(t,!1)}jt.checkLengthValue=l0;function c0(t){Xc(t,!0)}jt.checkOffsetValue=c0;function u0(t,e){if(t<0||t>e.length)throw new Error(gi.INVALID_TARGET_OFFSET)}jt.checkTargetOffset=u0;function f0(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t}function h0(t){if(typeof BigInt=="undefined")throw new Error("Platform does not support JS BigInt type.");if(typeof Kc.Buffer.prototype[t]=="undefined")throw new Error(`Platform does not support Buffer.prototype.${t}.`)}jt.bigIntAndBufferInt64Check=h0});var eu=S(Ns=>{"use strict";Object.defineProperty(Ns,"__esModule",{value:!0});var he=Jc(),Qc=4096,p0="utf8",Ls=class t{constructor(e){if(this.length=0,this._encoding=p0,this._writeOffset=0,this._readOffset=0,t.isSmartBufferOptions(e))if(e.encoding&&(he.checkEncoding(e.encoding),this._encoding=e.encoding),e.size)if(he.isFiniteInteger(e.size)&&e.size>0)this._buff=Buffer.allocUnsafe(e.size);else throw new Error(he.ERRORS.INVALID_SMARTBUFFER_SIZE);else if(e.buff)if(Buffer.isBuffer(e.buff))this._buff=e.buff,this.length=e.buff.length;else throw new Error(he.ERRORS.INVALID_SMARTBUFFER_BUFFER);else this._buff=Buffer.allocUnsafe(Qc);else{if(typeof e!="undefined")throw new Error(he.ERRORS.INVALID_SMARTBUFFER_OBJECT);this._buff=Buffer.allocUnsafe(Qc)}}static fromSize(e,i){return new this({size:e,encoding:i})}static fromBuffer(e,i){return new this({buff:e,encoding:i})}static fromOptions(e){return new this(e)}static isSmartBufferOptions(e){let i=e;return i&&(i.encoding!==void 0||i.size!==void 0||i.buff!==void 0)}readInt8(e){return this._readNumberValue(Buffer.prototype.readInt8,1,e)}readInt16BE(e){return this._readNumberValue(Buffer.prototype.readInt16BE,2,e)}readInt16LE(e){return this._readNumberValue(Buffer.prototype.readInt16LE,2,e)}readInt32BE(e){return this._readNumberValue(Buffer.prototype.readInt32BE,4,e)}readInt32LE(e){return this._readNumberValue(Buffer.prototype.readInt32LE,4,e)}readBigInt64BE(e){return he.bigIntAndBufferInt64Check("readBigInt64BE"),this._readNumberValue(Buffer.prototype.readBigInt64BE,8,e)}readBigInt64LE(e){return he.bigIntAndBufferInt64Check("readBigInt64LE"),this._readNumberValue(Buffer.prototype.readBigInt64LE,8,e)}writeInt8(e,i){return this._writeNumberValue(Buffer.prototype.writeInt8,1,e,i),this}insertInt8(e,i){return this._insertNumberValue(Buffer.prototype.writeInt8,1,e,i)}writeInt16BE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt16BE,2,e,i)}insertInt16BE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt16BE,2,e,i)}writeInt16LE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt16LE,2,e,i)}insertInt16LE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt16LE,2,e,i)}writeInt32BE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt32BE,4,e,i)}insertInt32BE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt32BE,4,e,i)}writeInt32LE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt32LE,4,e,i)}insertInt32LE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt32LE,4,e,i)}writeBigInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigInt64BE,8,e,i)}insertBigInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigInt64BE,8,e,i)}writeBigInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigInt64LE,8,e,i)}insertBigInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigInt64LE,8,e,i)}readUInt8(e){return this._readNumberValue(Buffer.prototype.readUInt8,1,e)}readUInt16BE(e){return this._readNumberValue(Buffer.prototype.readUInt16BE,2,e)}readUInt16LE(e){return this._readNumberValue(Buffer.prototype.readUInt16LE,2,e)}readUInt32BE(e){return this._readNumberValue(Buffer.prototype.readUInt32BE,4,e)}readUInt32LE(e){return this._readNumberValue(Buffer.prototype.readUInt32LE,4,e)}readBigUInt64BE(e){return he.bigIntAndBufferInt64Check("readBigUInt64BE"),this._readNumberValue(Buffer.prototype.readBigUInt64BE,8,e)}readBigUInt64LE(e){return he.bigIntAndBufferInt64Check("readBigUInt64LE"),this._readNumberValue(Buffer.prototype.readBigUInt64LE,8,e)}writeUInt8(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt8,1,e,i)}insertUInt8(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt8,1,e,i)}writeUInt16BE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt16BE,2,e,i)}insertUInt16BE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt16BE,2,e,i)}writeUInt16LE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt16LE,2,e,i)}insertUInt16LE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt16LE,2,e,i)}writeUInt32BE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt32BE,4,e,i)}insertUInt32BE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt32BE,4,e,i)}writeUInt32LE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt32LE,4,e,i)}insertUInt32LE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt32LE,4,e,i)}writeBigUInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,i)}insertBigUInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,i)}writeBigUInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,i)}insertBigUInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,i)}readFloatBE(e){return this._readNumberValue(Buffer.prototype.readFloatBE,4,e)}readFloatLE(e){return this._readNumberValue(Buffer.prototype.readFloatLE,4,e)}writeFloatBE(e,i){return this._writeNumberValue(Buffer.prototype.writeFloatBE,4,e,i)}insertFloatBE(e,i){return this._insertNumberValue(Buffer.prototype.writeFloatBE,4,e,i)}writeFloatLE(e,i){return this._writeNumberValue(Buffer.prototype.writeFloatLE,4,e,i)}insertFloatLE(e,i){return this._insertNumberValue(Buffer.prototype.writeFloatLE,4,e,i)}readDoubleBE(e){return this._readNumberValue(Buffer.prototype.readDoubleBE,8,e)}readDoubleLE(e){return this._readNumberValue(Buffer.prototype.readDoubleLE,8,e)}writeDoubleBE(e,i){return this._writeNumberValue(Buffer.prototype.writeDoubleBE,8,e,i)}insertDoubleBE(e,i){return this._insertNumberValue(Buffer.prototype.writeDoubleBE,8,e,i)}writeDoubleLE(e,i){return this._writeNumberValue(Buffer.prototype.writeDoubleLE,8,e,i)}insertDoubleLE(e,i){return this._insertNumberValue(Buffer.prototype.writeDoubleLE,8,e,i)}readString(e,i){let r;typeof e=="number"?(he.checkLengthValue(e),r=Math.min(e,this.length-this._readOffset)):(i=e,r=this.length-this._readOffset),typeof i!="undefined"&&he.checkEncoding(i);let n=this._buff.slice(this._readOffset,this._readOffset+r).toString(i||this._encoding);return this._readOffset+=r,n}insertString(e,i,r){return he.checkOffsetValue(i),this._handleString(e,!0,i,r)}writeString(e,i,r){return this._handleString(e,!1,i,r)}readStringNT(e){typeof e!="undefined"&&he.checkEncoding(e);let i=this.length;for(let n=this._readOffset;n<this.length;n++)if(this._buff[n]===0){i=n;break}let r=this._buff.slice(this._readOffset,i);return this._readOffset=i+1,r.toString(e||this._encoding)}insertStringNT(e,i,r){return he.checkOffsetValue(i),this.insertString(e,i,r),this.insertUInt8(0,i+e.length),this}writeStringNT(e,i,r){return this.writeString(e,i,r),this.writeUInt8(0,typeof i=="number"?i+e.length:this.writeOffset),this}readBuffer(e){typeof e!="undefined"&&he.checkLengthValue(e);let i=typeof e=="number"?e:this.length,r=Math.min(this.length,this._readOffset+i),n=this._buff.slice(this._readOffset,r);return this._readOffset=r,n}insertBuffer(e,i){return he.checkOffsetValue(i),this._handleBuffer(e,!0,i)}writeBuffer(e,i){return this._handleBuffer(e,!1,i)}readBufferNT(){let e=this.length;for(let r=this._readOffset;r<this.length;r++)if(this._buff[r]===0){e=r;break}let i=this._buff.slice(this._readOffset,e);return this._readOffset=e+1,i}insertBufferNT(e,i){return he.checkOffsetValue(i),this.insertBuffer(e,i),this.insertUInt8(0,i+e.length),this}writeBufferNT(e,i){return typeof i!="undefined"&&he.checkOffsetValue(i),this.writeBuffer(e,i),this.writeUInt8(0,typeof i=="number"?i+e.length:this._writeOffset),this}clear(){return this._writeOffset=0,this._readOffset=0,this.length=0,this}remaining(){return this.length-this._readOffset}get readOffset(){return this._readOffset}set readOffset(e){he.checkOffsetValue(e),he.checkTargetOffset(e,this),this._readOffset=e}get writeOffset(){return this._writeOffset}set writeOffset(e){he.checkOffsetValue(e),he.checkTargetOffset(e,this),this._writeOffset=e}get encoding(){return this._encoding}set encoding(e){he.checkEncoding(e),this._encoding=e}get internalBuffer(){return this._buff}toBuffer(){return this._buff.slice(0,this.length)}toString(e){let i=typeof e=="string"?e:this._encoding;return he.checkEncoding(i),this._buff.toString(i,0,this.length)}destroy(){return this.clear(),this}_handleString(e,i,r,n){let s=this._writeOffset,o=this._encoding;typeof r=="number"?s=r:typeof r=="string"&&(he.checkEncoding(r),o=r),typeof n=="string"&&(he.checkEncoding(n),o=n);let f=Buffer.byteLength(e,o);return i?this.ensureInsertable(f,s):this._ensureWriteable(f,s),this._buff.write(e,s,f,o),i?this._writeOffset+=f:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,s+f):this._writeOffset+=f,this}_handleBuffer(e,i,r){let n=typeof r=="number"?r:this._writeOffset;return i?this.ensureInsertable(e.length,n):this._ensureWriteable(e.length,n),e.copy(this._buff,n),i?this._writeOffset+=e.length:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,n+e.length):this._writeOffset+=e.length,this}ensureReadable(e,i){let r=this._readOffset;if(typeof i!="undefined"&&(he.checkOffsetValue(i),r=i),r<0||r+e>this.length)throw new Error(he.ERRORS.INVALID_READ_BEYOND_BOUNDS)}ensureInsertable(e,i){he.checkOffsetValue(i),this._ensureCapacity(this.length+e),i<this.length&&this._buff.copy(this._buff,i+e,i,this._buff.length),i+e>this.length?this.length=i+e:this.length+=e}_ensureWriteable(e,i){let r=typeof i=="number"?i:this._writeOffset;this._ensureCapacity(r+e),r+e>this.length&&(this.length=r+e)}_ensureCapacity(e){let i=this._buff.length;if(e>i){let r=this._buff,n=i*3/2+1;n<e&&(n=e),this._buff=Buffer.allocUnsafe(n),r.copy(this._buff,0,0,i)}}_readNumberValue(e,i,r){this.ensureReadable(i,r);let n=e.call(this._buff,typeof r=="number"?r:this._readOffset);return typeof r=="undefined"&&(this._readOffset+=i),n}_insertNumberValue(e,i,r,n){return he.checkOffsetValue(n),this.ensureInsertable(i,n),e.call(this._buff,r,n),this._writeOffset+=i,this}_writeNumberValue(e,i,r,n){if(typeof n=="number"){if(n<0)throw new Error(he.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);he.checkOffsetValue(n)}let s=typeof n=="number"?n:this._writeOffset;return this._ensureWriteable(i,s),e.call(this._buff,r,s),typeof n=="number"?this._writeOffset=Math.max(this._writeOffset,s+i):this._writeOffset+=i,this}};Ns.SmartBuffer=Ls});var Fs=S(Ie=>{"use strict";Object.defineProperty(Ie,"__esModule",{value:!0});Ie.SOCKS5_NO_ACCEPTABLE_AUTH=Ie.SOCKS5_CUSTOM_AUTH_END=Ie.SOCKS5_CUSTOM_AUTH_START=Ie.SOCKS_INCOMING_PACKET_SIZES=Ie.SocksClientState=Ie.Socks5Response=Ie.Socks5HostType=Ie.Socks5Auth=Ie.Socks4Response=Ie.SocksCommand=Ie.ERRORS=Ie.DEFAULT_TIMEOUT=void 0;var d0=3e4;Ie.DEFAULT_TIMEOUT=d0;var m0={InvalidSocksCommand:"An invalid SOCKS command was provided. Valid options are connect, bind, and associate.",InvalidSocksCommandForOperation:"An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.",InvalidSocksCommandChain:"An invalid SOCKS command was provided. Chaining currently only supports the connect command.",InvalidSocksClientOptionsDestination:"An invalid destination host was provided.",InvalidSocksClientOptionsExistingSocket:"An invalid existing socket was provided. This should be an instance of stream.Duplex.",InvalidSocksClientOptionsProxy:"Invalid SOCKS proxy details were provided.",InvalidSocksClientOptionsTimeout:"An invalid timeout value was provided. Please enter a value above 0 (in ms).",InvalidSocksClientOptionsProxiesLength:"At least two socks proxies must be provided for chaining.",InvalidSocksClientOptionsCustomAuthRange:"Custom auth must be a value between 0x80 and 0xFE.",InvalidSocksClientOptionsCustomAuthOptions:"When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.",NegotiationError:"Negotiation error",SocketClosed:"Socket closed",ProxyConnectionTimedOut:"Proxy connection timed out",InternalError:"SocksClient internal error (this should not happen)",InvalidSocks4HandshakeResponse:"Received invalid Socks4 handshake response",Socks4ProxyRejectedConnection:"Socks4 Proxy rejected connection",InvalidSocks4IncomingConnectionResponse:"Socks4 invalid incoming connection response",Socks4ProxyRejectedIncomingBoundConnection:"Socks4 Proxy rejected incoming bound connection",InvalidSocks5InitialHandshakeResponse:"Received invalid Socks5 initial handshake response",InvalidSocks5IntiailHandshakeSocksVersion:"Received invalid Socks5 initial handshake (invalid socks version)",InvalidSocks5InitialHandshakeNoAcceptedAuthType:"Received invalid Socks5 initial handshake (no accepted authentication type)",InvalidSocks5InitialHandshakeUnknownAuthType:"Received invalid Socks5 initial handshake (unknown authentication type)",Socks5AuthenticationFailed:"Socks5 Authentication failed",InvalidSocks5FinalHandshake:"Received invalid Socks5 final handshake response",InvalidSocks5FinalHandshakeRejected:"Socks5 proxy rejected connection",InvalidSocks5IncomingConnectionResponse:"Received invalid Socks5 incoming connection response",Socks5ProxyRejectedIncomingBoundConnection:"Socks5 Proxy rejected incoming bound connection"};Ie.ERRORS=m0;var g0={Socks5InitialHandshakeResponse:2,Socks5UserPassAuthenticationResponse:2,Socks5ResponseHeader:5,Socks5ResponseIPv4:10,Socks5ResponseIPv6:22,Socks5ResponseHostname:t=>t+7,Socks4Response:8};Ie.SOCKS_INCOMING_PACKET_SIZES=g0;var tu;(function(t){t[t.connect=1]="connect",t[t.bind=2]="bind",t[t.associate=3]="associate"})(tu||(Ie.SocksCommand=tu={}));var iu;(function(t){t[t.Granted=90]="Granted",t[t.Failed=91]="Failed",t[t.Rejected=92]="Rejected",t[t.RejectedIdent=93]="RejectedIdent"})(iu||(Ie.Socks4Response=iu={}));var ru;(function(t){t[t.NoAuth=0]="NoAuth",t[t.GSSApi=1]="GSSApi",t[t.UserPass=2]="UserPass"})(ru||(Ie.Socks5Auth=ru={}));var v0=128;Ie.SOCKS5_CUSTOM_AUTH_START=v0;var _0=254;Ie.SOCKS5_CUSTOM_AUTH_END=_0;var x0=255;Ie.SOCKS5_NO_ACCEPTABLE_AUTH=x0;var nu;(function(t){t[t.Granted=0]="Granted",t[t.Failure=1]="Failure",t[t.NotAllowed=2]="NotAllowed",t[t.NetworkUnreachable=3]="NetworkUnreachable",t[t.HostUnreachable=4]="HostUnreachable",t[t.ConnectionRefused=5]="ConnectionRefused",t[t.TTLExpired=6]="TTLExpired",t[t.CommandNotSupported=7]="CommandNotSupported",t[t.AddressNotSupported=8]="AddressNotSupported"})(nu||(Ie.Socks5Response=nu={}));var su;(function(t){t[t.IPv4=1]="IPv4",t[t.Hostname=3]="Hostname",t[t.IPv6=4]="IPv6"})(su||(Ie.Socks5HostType=su={}));var ou;(function(t){t[t.Created=0]="Created",t[t.Connecting=1]="Connecting",t[t.Connected=2]="Connected",t[t.SentInitialHandshake=3]="SentInitialHandshake",t[t.ReceivedInitialHandshakeResponse=4]="ReceivedInitialHandshakeResponse",t[t.SentAuthentication=5]="SentAuthentication",t[t.ReceivedAuthenticationResponse=6]="ReceivedAuthenticationResponse",t[t.SentFinalHandshake=7]="SentFinalHandshake",t[t.ReceivedFinalResponse=8]="ReceivedFinalResponse",t[t.BoundWaitingForConnection=9]="BoundWaitingForConnection",t[t.Established=10]="Established",t[t.Disconnected=11]="Disconnected",t[t.Error=99]="Error"})(ou||(Ie.SocksClientState=ou={}))});var Ds=S($i=>{"use strict";Object.defineProperty($i,"__esModule",{value:!0});$i.shuffleArray=$i.SocksClientError=void 0;var Ms=class extends Error{constructor(e,i){super(e),this.options=i}};$i.SocksClientError=Ms;function y0(t){for(let e=t.length-1;e>0;e--){let i=Math.floor(Math.random()*(e+1));[t[e],t[i]]=[t[i],t[e]]}}$i.shuffleArray=y0});var Us=S(Gi=>{"use strict";Object.defineProperty(Gi,"__esModule",{value:!0});Gi.isCorrect=Gi.isInSubnet=void 0;function b0(t){return this.subnetMask<t.subnetMask?!1:this.mask(t.subnetMask)===t.mask()}Gi.isInSubnet=b0;function w0(t){return function(){return this.addressMinusSuffix!==this.correctForm()?!1:this.subnetMask===t&&!this.parsedSubnet?!0:this.parsedSubnet===String(this.subnetMask)}}Gi.isCorrect=w0});var js=S(Rt=>{"use strict";Object.defineProperty(Rt,"__esModule",{value:!0});Rt.RE_SUBNET_STRING=Rt.RE_ADDRESS=Rt.GROUPS=Rt.BITS=void 0;Rt.BITS=32;Rt.GROUPS=4;Rt.RE_ADDRESS=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;Rt.RE_SUBNET_STRING=/\/\d{1,2}$/});var tn=S(en=>{"use strict";Object.defineProperty(en,"__esModule",{value:!0});en.AddressError=void 0;var qs=class extends Error{constructor(e,i){super(e),this.name="AddressError",i!==null&&(this.parseMessage=i)}};en.AddressError=qs});var Hs=S((rn,au)=>{(function(){var t,e=0xdeadbeefcafe,i=(e&16777215)==15715070;function r(a,c,p){a!=null&&(typeof a=="number"?this.fromNumber(a,c,p):c==null&&typeof a!="string"?this.fromString(a,256):this.fromString(a,c))}function n(){return new r(null)}function s(a,c,p,g,O,I){for(;--I>=0;){var U=c*this[a++]+p[g]+O;O=Math.floor(U/67108864),p[g++]=U&67108863}return O}function o(a,c,p,g,O,I){for(var U=c&32767,q=c>>15;--I>=0;){var Re=this[a]&32767,qe=this[a++]>>15,gt=q*Re+qe*U;Re=U*Re+((gt&32767)<<15)+p[g]+(O&1073741823),O=(Re>>>30)+(gt>>>15)+q*qe+(O>>>30),p[g++]=Re&1073741823}return O}function f(a,c,p,g,O,I){for(var U=c&16383,q=c>>14;--I>=0;){var Re=this[a]&16383,qe=this[a++]>>14,gt=q*Re+qe*U;Re=U*Re+((gt&16383)<<14)+p[g]+O,O=(Re>>28)+(gt>>14)+q*qe,p[g++]=Re&268435455}return O}var u=typeof navigator!="undefined";u&&i&&navigator.appName=="Microsoft Internet Explorer"?(r.prototype.am=o,t=30):u&&i&&navigator.appName!="Netscape"?(r.prototype.am=s,t=26):(r.prototype.am=f,t=28),r.prototype.DB=t,r.prototype.DM=(1<<t)-1,r.prototype.DV=1<<t;var h=52;r.prototype.FV=Math.pow(2,h),r.prototype.F1=h-t,r.prototype.F2=2*t-h;var l="0123456789abcdefghijklmnopqrstuvwxyz",d=new Array,m,_;for(m="0".charCodeAt(0),_=0;_<=9;++_)d[m++]=_;for(m="a".charCodeAt(0),_=10;_<36;++_)d[m++]=_;for(m="A".charCodeAt(0),_=10;_<36;++_)d[m++]=_;function v(a){return l.charAt(a)}function x(a,c){var p=d[a.charCodeAt(c)];return p==null?-1:p}function b(a){for(var c=this.t-1;c>=0;--c)a[c]=this[c];a.t=this.t,a.s=this.s}function T(a){this.t=1,this.s=a<0?-1:0,a>0?this[0]=a:a<-1?this[0]=a+this.DV:this.t=0}function B(a){var c=n();return c.fromInt(a),c}function E(a,c){var p;if(c==16)p=4;else if(c==8)p=3;else if(c==256)p=8;else if(c==2)p=1;else if(c==32)p=5;else if(c==4)p=2;else{this.fromRadix(a,c);return}this.t=0,this.s=0;for(var g=a.length,O=!1,I=0;--g>=0;){var U=p==8?a[g]&255:x(a,g);if(U<0){a.charAt(g)=="-"&&(O=!0);continue}O=!1,I==0?this[this.t++]=U:I+p>this.DB?(this[this.t-1]|=(U&(1<<this.DB-I)-1)<<I,this[this.t++]=U>>this.DB-I):this[this.t-1]|=U<<I,I+=p,I>=this.DB&&(I-=this.DB)}p==8&&a[0]&128&&(this.s=-1,I>0&&(this[this.t-1]|=(1<<this.DB-I)-1<<I)),this.clamp(),O&&r.ZERO.subTo(this,this)}function R(){for(var a=this.s&this.DM;this.t>0&&this[this.t-1]==a;)--this.t}function k(a){if(this.s<0)return"-"+this.negate().toString(a);var c;if(a==16)c=4;else if(a==8)c=3;else if(a==2)c=1;else if(a==32)c=5;else if(a==4)c=2;else return this.toRadix(a);var p=(1<<c)-1,g,O=!1,I="",U=this.t,q=this.DB-U*this.DB%c;if(U-- >0)for(q<this.DB&&(g=this[U]>>q)>0&&(O=!0,I=v(g));U>=0;)q<c?(g=(this[U]&(1<<q)-1)<<c-q,g|=this[--U]>>(q+=this.DB-c)):(g=this[U]>>(q-=c)&p,q<=0&&(q+=this.DB,--U)),g>0&&(O=!0),O&&(I+=v(g));return O?I:"0"}function X(){var a=n();return r.ZERO.subTo(this,a),a}function A(){return this.s<0?this.negate():this}function z(a){var c=this.s-a.s;if(c!=0)return c;var p=this.t;if(c=p-a.t,c!=0)return this.s<0?-c:c;for(;--p>=0;)if((c=this[p]-a[p])!=0)return c;return 0}function C(a){var c=1,p;return(p=a>>>16)!=0&&(a=p,c+=16),(p=a>>8)!=0&&(a=p,c+=8),(p=a>>4)!=0&&(a=p,c+=4),(p=a>>2)!=0&&(a=p,c+=2),(p=a>>1)!=0&&(a=p,c+=1),c}function N(){return this.t<=0?0:this.DB*(this.t-1)+C(this[this.t-1]^this.s&this.DM)}function D(a,c){var p;for(p=this.t-1;p>=0;--p)c[p+a]=this[p];for(p=a-1;p>=0;--p)c[p]=0;c.t=this.t+a,c.s=this.s}function J(a,c){for(var p=a;p<this.t;++p)c[p-a]=this[p];c.t=Math.max(this.t-a,0),c.s=this.s}function j(a,c){var p=a%this.DB,g=this.DB-p,O=(1<<g)-1,I=Math.floor(a/this.DB),U=this.s<<p&this.DM,q;for(q=this.t-1;q>=0;--q)c[q+I+1]=this[q]>>g|U,U=(this[q]&O)<<p;for(q=I-1;q>=0;--q)c[q]=0;c[I]=U,c.t=this.t+I+1,c.s=this.s,c.clamp()}function se(a,c){c.s=this.s;var p=Math.floor(a/this.DB);if(p>=this.t){c.t=0;return}var g=a%this.DB,O=this.DB-g,I=(1<<g)-1;c[0]=this[p]>>g;for(var U=p+1;U<this.t;++U)c[U-p-1]|=(this[U]&I)<<O,c[U-p]=this[U]>>g;g>0&&(c[this.t-p-1]|=(this.s&I)<<O),c.t=this.t-p,c.clamp()}function M(a,c){for(var p=0,g=0,O=Math.min(a.t,this.t);p<O;)g+=this[p]-a[p],c[p++]=g&this.DM,g>>=this.DB;if(a.t<this.t){for(g-=a.s;p<this.t;)g+=this[p],c[p++]=g&this.DM,g>>=this.DB;g+=this.s}else{for(g+=this.s;p<a.t;)g-=a[p],c[p++]=g&this.DM,g>>=this.DB;g-=a.s}c.s=g<0?-1:0,g<-1?c[p++]=this.DV+g:g>0&&(c[p++]=g),c.t=p,c.clamp()}function $(a,c){var p=this.abs(),g=a.abs(),O=p.t;for(c.t=O+g.t;--O>=0;)c[O]=0;for(O=0;O<g.t;++O)c[O+p.t]=p.am(0,g[O],c,O,0,p.t);c.s=0,c.clamp(),this.s!=a.s&&r.ZERO.subTo(c,c)}function Y(a){for(var c=this.abs(),p=a.t=2*c.t;--p>=0;)a[p]=0;for(p=0;p<c.t-1;++p){var g=c.am(p,c[p],a,2*p,0,1);(a[p+c.t]+=c.am(p+1,2*c[p],a,2*p+1,g,c.t-p-1))>=c.DV&&(a[p+c.t]-=c.DV,a[p+c.t+1]=1)}a.t>0&&(a[a.t-1]+=c.am(p,c[p],a,2*p,0,1)),a.s=0,a.clamp()}function Q(a,c,p){var g=a.abs();if(!(g.t<=0)){var O=this.abs();if(O.t<g.t){c!=null&&c.fromInt(0),p!=null&&this.copyTo(p);return}p==null&&(p=n());var I=n(),U=this.s,q=a.s,Re=this.DB-C(g[g.t-1]);Re>0?(g.lShiftTo(Re,I),O.lShiftTo(Re,p)):(g.copyTo(I),O.copyTo(p));var qe=I.t,gt=I[qe-1];if(gt!=0){var ft=gt*(1<<this.F1)+(qe>1?I[qe-2]>>this.F2:0),Ut=this.FV/ft,Mr=(1<<this.F1)/ft,wt=1<<this.F2,St=p.t,Dr=St-qe,Wt=c==null?n():c;for(I.dlShiftTo(Dr,Wt),p.compareTo(Wt)>=0&&(p[p.t++]=1,p.subTo(Wt,p)),r.ONE.dlShiftTo(qe,Wt),Wt.subTo(I,I);I.t<qe;)I[I.t++]=0;for(;--Dr>=0;){var Rn=p[--St]==gt?this.DM:Math.floor(p[St]*Ut+(p[St-1]+wt)*Mr);if((p[St]+=I.am(0,Rn,p,Dr,0,qe))<Rn)for(I.dlShiftTo(Dr,Wt),p.subTo(Wt,p);p[St]<--Rn;)p.subTo(Wt,p)}c!=null&&(p.drShiftTo(qe,c),U!=q&&r.ZERO.subTo(c,c)),p.t=qe,p.clamp(),Re>0&&p.rShiftTo(Re,p),U<0&&r.ZERO.subTo(p,p)}}}function H(a){var c=n();return this.abs().divRemTo(a,null,c),this.s<0&&c.compareTo(r.ZERO)>0&&a.subTo(c,c),c}function we(a){this.m=a}function de(a){return a.s<0||a.compareTo(this.m)>=0?a.mod(this.m):a}function le(a){return a}function ce(a){a.divRemTo(this.m,null,a)}function w(a,c,p){a.multiplyTo(c,p),this.reduce(p)}function K(a,c){a.squareTo(c),this.reduce(c)}we.prototype.convert=de,we.prototype.revert=le,we.prototype.reduce=ce,we.prototype.mulTo=w,we.prototype.sqrTo=K;function Ee(){if(this.t<1)return 0;var a=this[0];if(!(a&1))return 0;var c=a&3;return c=c*(2-(a&15)*c)&15,c=c*(2-(a&255)*c)&255,c=c*(2-((a&65535)*c&65535))&65535,c=c*(2-a*c%this.DV)%this.DV,c>0?this.DV-c:-c}function _e(a){this.m=a,this.mp=a.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<a.DB-15)-1,this.mt2=2*a.t}function me(a){var c=n();return a.abs().dlShiftTo(this.m.t,c),c.divRemTo(this.m,null,c),a.s<0&&c.compareTo(r.ZERO)>0&&this.m.subTo(c,c),c}function ge(a){var c=n();return a.copyTo(c),this.reduce(c),c}function ue(a){for(;a.t<=this.mt2;)a[a.t++]=0;for(var c=0;c<this.m.t;++c){var p=a[c]&32767,g=p*this.mpl+((p*this.mph+(a[c]>>15)*this.mpl&this.um)<<15)&a.DM;for(p=c+this.m.t,a[p]+=this.m.am(0,g,a,c,0,this.m.t);a[p]>=a.DV;)a[p]-=a.DV,a[++p]++}a.clamp(),a.drShiftTo(this.m.t,a),a.compareTo(this.m)>=0&&a.subTo(this.m,a)}function V(a,c){a.squareTo(c),this.reduce(c)}function F(a,c,p){a.multiplyTo(c,p),this.reduce(p)}_e.prototype.convert=me,_e.prototype.revert=ge,_e.prototype.reduce=ue,_e.prototype.mulTo=F,_e.prototype.sqrTo=V;function ke(){return(this.t>0?this[0]&1:this.s)==0}function ie(a,c){if(a>4294967295||a<1)return r.ONE;var p=n(),g=n(),O=c.convert(this),I=C(a)-1;for(O.copyTo(p);--I>=0;)if(c.sqrTo(p,g),(a&1<<I)>0)c.mulTo(g,O,p);else{var U=p;p=g,g=U}return c.revert(p)}function fe(a,c){var p;return a<256||c.isEven()?p=new we(c):p=new _e(c),this.exp(a,p)}r.prototype.copyTo=b,r.prototype.fromInt=T,r.prototype.fromString=E,r.prototype.clamp=R,r.prototype.dlShiftTo=D,r.prototype.drShiftTo=J,r.prototype.lShiftTo=j,r.prototype.rShiftTo=se,r.prototype.subTo=M,r.prototype.multiplyTo=$,r.prototype.squareTo=Y,r.prototype.divRemTo=Q,r.prototype.invDigit=Ee,r.prototype.isEven=ke,r.prototype.exp=ie,r.prototype.toString=k,r.prototype.negate=X,r.prototype.abs=A,r.prototype.compareTo=z,r.prototype.bitLength=N,r.prototype.mod=H,r.prototype.modPowInt=fe,r.ZERO=B(0),r.ONE=B(1);function ot(){var a=n();return this.copyTo(a),a}function ct(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function y(){return this.t==0?this.s:this[0]<<24>>24}function W(){return this.t==0?this.s:this[0]<<16>>16}function ee(a){return Math.floor(Math.LN2*this.DB/Math.log(a))}function Z(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function oe(a){if(a==null&&(a=10),this.signum()==0||a<2||a>36)return"0";var c=this.chunkSize(a),p=Math.pow(a,c),g=B(p),O=n(),I=n(),U="";for(this.divRemTo(g,O,I);O.signum()>0;)U=(p+I.intValue()).toString(a).substr(1)+U,O.divRemTo(g,O,I);return I.intValue().toString(a)+U}function ae(a,c){this.fromInt(0),c==null&&(c=10);for(var p=this.chunkSize(c),g=Math.pow(c,p),O=!1,I=0,U=0,q=0;q<a.length;++q){var Re=x(a,q);if(Re<0){a.charAt(q)=="-"&&this.signum()==0&&(O=!0);continue}U=c*U+Re,++I>=p&&(this.dMultiply(g),this.dAddOffset(U,0),I=0,U=0)}I>0&&(this.dMultiply(Math.pow(c,I)),this.dAddOffset(U,0)),O&&r.ZERO.subTo(this,this)}function xe(a,c,p){if(typeof c=="number")if(a<2)this.fromInt(1);else for(this.fromNumber(a,p),this.testBit(a-1)||this.bitwiseTo(r.ONE.shiftLeft(a-1),ne,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(c);)this.dAddOffset(2,0),this.bitLength()>a&&this.subTo(r.ONE.shiftLeft(a-1),this);else{var g=new Array,O=a&7;g.length=(a>>3)+1,c.nextBytes(g),O>0?g[0]&=(1<<O)-1:g[0]=0,this.fromString(g,256)}}function ye(){var a=this.t,c=new Array;c[0]=this.s;var p=this.DB-a*this.DB%8,g,O=0;if(a-- >0)for(p<this.DB&&(g=this[a]>>p)!=(this.s&this.DM)>>p&&(c[O++]=g|this.s<<this.DB-p);a>=0;)p<8?(g=(this[a]&(1<<p)-1)<<8-p,g|=this[--a]>>(p+=this.DB-8)):(g=this[a]>>(p-=8)&255,p<=0&&(p+=this.DB,--a)),g&128&&(g|=-256),O==0&&(this.s&128)!=(g&128)&&++O,(O>0||g!=this.s)&&(c[O++]=g);return c}function Be(a){return this.compareTo(a)==0}function Ce(a){return this.compareTo(a)<0?this:a}function L(a){return this.compareTo(a)>0?this:a}function G(a,c,p){var g,O,I=Math.min(a.t,this.t);for(g=0;g<I;++g)p[g]=c(this[g],a[g]);if(a.t<this.t){for(O=a.s&this.DM,g=I;g<this.t;++g)p[g]=c(this[g],O);p.t=this.t}else{for(O=this.s&this.DM,g=I;g<a.t;++g)p[g]=c(O,a[g]);p.t=a.t}p.s=c(this.s,a.s),p.clamp()}function re(a,c){return a&c}function Te(a){var c=n();return this.bitwiseTo(a,re,c),c}function ne(a,c){return a|c}function pe(a){var c=n();return this.bitwiseTo(a,ne,c),c}function Se(a,c){return a^c}function te(a){var c=n();return this.bitwiseTo(a,Se,c),c}function be(a,c){return a&~c}function Me(a){var c=n();return this.bitwiseTo(a,be,c),c}function Oe(){for(var a=n(),c=0;c<this.t;++c)a[c]=this.DM&~this[c];return a.t=this.t,a.s=~this.s,a}function at(a){var c=n();return a<0?this.rShiftTo(-a,c):this.lShiftTo(a,c),c}function mt(a){var c=n();return a<0?this.lShiftTo(-a,c):this.rShiftTo(a,c),c}function Mt(a){if(a==0)return-1;var c=0;return a&65535||(a>>=16,c+=16),a&255||(a>>=8,c+=8),a&15||(a>>=4,c+=4),a&3||(a>>=2,c+=2),a&1||++c,c}function Gt(){for(var a=0;a<this.t;++a)if(this[a]!=0)return a*this.DB+Mt(this[a]);return this.s<0?this.t*this.DB:-1}function zt(a){for(var c=0;a!=0;)a&=a-1,++c;return c}function ri(){for(var a=0,c=this.s&this.DM,p=0;p<this.t;++p)a+=zt(this[p]^c);return a}function ni(a){var c=Math.floor(a/this.DB);return c>=this.t?this.s!=0:(this[c]&1<<a%this.DB)!=0}function Oi(a,c){var p=r.ONE.shiftLeft(a);return this.bitwiseTo(p,c,p),p}function si(a){return this.changeBit(a,ne)}function oi(a){return this.changeBit(a,be)}function ai(a){return this.changeBit(a,Se)}function li(a,c){for(var p=0,g=0,O=Math.min(a.t,this.t);p<O;)g+=this[p]+a[p],c[p++]=g&this.DM,g>>=this.DB;if(a.t<this.t){for(g+=a.s;p<this.t;)g+=this[p],c[p++]=g&this.DM,g>>=this.DB;g+=this.s}else{for(g+=this.s;p<a.t;)g+=a[p],c[p++]=g&this.DM,g>>=this.DB;g+=a.s}c.s=g<0?-1:0,g>0?c[p++]=g:g<-1&&(c[p++]=this.DV+g),c.t=p,c.clamp()}function Er(a){var c=n();return this.addTo(a,c),c}function nr(a){var c=n();return this.subTo(a,c),c}function kr(a){var c=n();return this.multiplyTo(a,c),c}function Cr(){var a=n();return this.squareTo(a),a}function Or(a){var c=n();return this.divRemTo(a,c,null),c}function Tr(a){var c=n();return this.divRemTo(a,null,c),c}function Ir(a){var c=n(),p=n();return this.divRemTo(a,c,p),new Array(c,p)}function Tn(a){this[this.t]=this.am(0,a-1,this,0,0,this.t),++this.t,this.clamp()}function ci(a,c){if(a!=0){for(;this.t<=c;)this[this.t++]=0;for(this[c]+=a;this[c]>=this.DV;)this[c]-=this.DV,++c>=this.t&&(this[this.t++]=0),++this[c]}}function Dt(){}function ui(a){return a}function Ti(a,c,p){a.multiplyTo(c,p)}function Ar(a,c){a.squareTo(c)}Dt.prototype.convert=ui,Dt.prototype.revert=ui,Dt.prototype.mulTo=Ti,Dt.prototype.sqrTo=Ar;function Br(a){return this.exp(a,new Dt)}function Rr(a,c,p){var g=Math.min(this.t+a.t,c);for(p.s=0,p.t=g;g>0;)p[--g]=0;var O;for(O=p.t-this.t;g<O;++g)p[g+this.t]=this.am(0,a[g],p,g,0,this.t);for(O=Math.min(a.t,c);g<O;++g)this.am(0,a[g],p,g,0,c-g);p.clamp()}function Pr(a,c,p){--c;var g=p.t=this.t+a.t-c;for(p.s=0;--g>=0;)p[g]=0;for(g=Math.max(c-this.t,0);g<a.t;++g)p[this.t+g-c]=this.am(c-g,a[g],p,0,0,this.t+g-c);p.clamp(),p.drShiftTo(1,p)}function At(a){this.r2=n(),this.q3=n(),r.ONE.dlShiftTo(2*a.t,this.r2),this.mu=this.r2.divide(a),this.m=a}function Lr(a){if(a.s<0||a.t>2*this.m.t)return a.mod(this.m);if(a.compareTo(this.m)<0)return a;var c=n();return a.copyTo(c),this.reduce(c),c}function Nr(a){return a}function Ii(a){for(a.drShiftTo(this.m.t-1,this.r2),a.t>this.m.t+1&&(a.t=this.m.t+1,a.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);a.compareTo(this.r2)<0;)a.dAddOffset(1,this.m.t+1);for(a.subTo(this.r2,a);a.compareTo(this.m)>=0;)a.subTo(this.m,a)}function Ph(a,c){a.squareTo(c),this.reduce(c)}function Lh(a,c,p){a.multiplyTo(c,p),this.reduce(p)}At.prototype.convert=Lr,At.prototype.revert=Nr,At.prototype.reduce=Ii,At.prototype.mulTo=Lh,At.prototype.sqrTo=Ph;function Nh(a,c){var p=a.bitLength(),g,O=B(1),I;if(p<=0)return O;p<18?g=1:p<48?g=3:p<144?g=4:p<768?g=5:g=6,p<8?I=new we(c):c.isEven()?I=new At(c):I=new _e(c);var U=new Array,q=3,Re=g-1,qe=(1<<g)-1;if(U[1]=I.convert(this),g>1){var gt=n();for(I.sqrTo(U[1],gt);q<=qe;)U[q]=n(),I.mulTo(gt,U[q-2],U[q]),q+=2}var ft=a.t-1,Ut,Mr=!0,wt=n(),St;for(p=C(a[ft])-1;ft>=0;){for(p>=Re?Ut=a[ft]>>p-Re&qe:(Ut=(a[ft]&(1<<p+1)-1)<<Re-p,ft>0&&(Ut|=a[ft-1]>>this.DB+p-Re)),q=g;!(Ut&1);)Ut>>=1,--q;if((p-=q)<0&&(p+=this.DB,--ft),Mr)U[Ut].copyTo(O),Mr=!1;else{for(;q>1;)I.sqrTo(O,wt),I.sqrTo(wt,O),q-=2;q>0?I.sqrTo(O,wt):(St=O,O=wt,wt=St),I.mulTo(wt,U[Ut],O)}for(;ft>=0&&!(a[ft]&1<<p);)I.sqrTo(O,wt),St=O,O=wt,wt=St,--p<0&&(p=this.DB-1,--ft)}return I.revert(O)}function Fh(a){var c=this.s<0?this.negate():this.clone(),p=a.s<0?a.negate():a.clone();if(c.compareTo(p)<0){var g=c;c=p,p=g}var O=c.getLowestSetBit(),I=p.getLowestSetBit();if(I<0)return c;for(O<I&&(I=O),I>0&&(c.rShiftTo(I,c),p.rShiftTo(I,p));c.signum()>0;)(O=c.getLowestSetBit())>0&&c.rShiftTo(O,c),(O=p.getLowestSetBit())>0&&p.rShiftTo(O,p),c.compareTo(p)>=0?(c.subTo(p,c),c.rShiftTo(1,c)):(p.subTo(c,p),p.rShiftTo(1,p));return I>0&&p.lShiftTo(I,p),p}function Mh(a){if(a<=0)return 0;var c=this.DV%a,p=this.s<0?a-1:0;if(this.t>0)if(c==0)p=this[0]%a;else for(var g=this.t-1;g>=0;--g)p=(c*p+this[g])%a;return p}function Dh(a){var c=a.isEven();if(this.isEven()&&c||a.signum()==0)return r.ZERO;for(var p=a.clone(),g=this.clone(),O=B(1),I=B(0),U=B(0),q=B(1);p.signum()!=0;){for(;p.isEven();)p.rShiftTo(1,p),c?((!O.isEven()||!I.isEven())&&(O.addTo(this,O),I.subTo(a,I)),O.rShiftTo(1,O)):I.isEven()||I.subTo(a,I),I.rShiftTo(1,I);for(;g.isEven();)g.rShiftTo(1,g),c?((!U.isEven()||!q.isEven())&&(U.addTo(this,U),q.subTo(a,q)),U.rShiftTo(1,U)):q.isEven()||q.subTo(a,q),q.rShiftTo(1,q);p.compareTo(g)>=0?(p.subTo(g,p),c&&O.subTo(U,O),I.subTo(q,I)):(g.subTo(p,g),c&&U.subTo(O,U),q.subTo(I,q))}if(g.compareTo(r.ONE)!=0)return r.ZERO;if(q.compareTo(a)>=0)return q.subtract(a);if(q.signum()<0)q.addTo(a,q);else return q;return q.signum()<0?q.add(a):q}var et=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Uh=(1<<26)/et[et.length-1];function jh(a){var c,p=this.abs();if(p.t==1&&p[0]<=et[et.length-1]){for(c=0;c<et.length;++c)if(p[0]==et[c])return!0;return!1}if(p.isEven())return!1;for(c=1;c<et.length;){for(var g=et[c],O=c+1;O<et.length&&g<Uh;)g*=et[O++];for(g=p.modInt(g);c<O;)if(g%et[c++]==0)return!1}return p.millerRabin(a)}function qh(a){var c=this.subtract(r.ONE),p=c.getLowestSetBit();if(p<=0)return!1;var g=c.shiftRight(p);a=a+1>>1,a>et.length&&(a=et.length);for(var O=n(),I=0;I<a;++I){O.fromInt(et[Math.floor(Math.random()*et.length)]);var U=O.modPow(g,this);if(U.compareTo(r.ONE)!=0&&U.compareTo(c)!=0){for(var q=1;q++<p&&U.compareTo(c)!=0;)if(U=U.modPowInt(2,this),U.compareTo(r.ONE)==0)return!1;if(U.compareTo(c)!=0)return!1}}return!0}r.prototype.chunkSize=ee,r.prototype.toRadix=oe,r.prototype.fromRadix=ae,r.prototype.fromNumber=xe,r.prototype.bitwiseTo=G,r.prototype.changeBit=Oi,r.prototype.addTo=li,r.prototype.dMultiply=Tn,r.prototype.dAddOffset=ci,r.prototype.multiplyLowerTo=Rr,r.prototype.multiplyUpperTo=Pr,r.prototype.modInt=Mh,r.prototype.millerRabin=qh,r.prototype.clone=ot,r.prototype.intValue=ct,r.prototype.byteValue=y,r.prototype.shortValue=W,r.prototype.signum=Z,r.prototype.toByteArray=ye,r.prototype.equals=Be,r.prototype.min=Ce,r.prototype.max=L,r.prototype.and=Te,r.prototype.or=pe,r.prototype.xor=te,r.prototype.andNot=Me,r.prototype.not=Oe,r.prototype.shiftLeft=at,r.prototype.shiftRight=mt,r.prototype.getLowestSetBit=Gt,r.prototype.bitCount=ri,r.prototype.testBit=ni,r.prototype.setBit=si,r.prototype.clearBit=oi,r.prototype.flipBit=ai,r.prototype.add=Er,r.prototype.subtract=nr,r.prototype.multiply=kr,r.prototype.divide=Or,r.prototype.remainder=Tr,r.prototype.divideAndRemainder=Ir,r.prototype.modPow=Nh,r.prototype.modInverse=Dh,r.prototype.pow=Br,r.prototype.gcd=Fh,r.prototype.isProbablePrime=jh,r.prototype.square=Cr,r.prototype.Barrett=At;var Fr,ut,je;function Hh(a){ut[je++]^=a&255,ut[je++]^=a>>8&255,ut[je++]^=a>>16&255,ut[je++]^=a>>24&255,je>=Bn&&(je-=Bn)}function qo(){Hh(new Date().getTime())}if(ut==null){ut=new Array,je=0;var bt;if(typeof window!="undefined"&&window.crypto){if(window.crypto.getRandomValues){var Ho=new Uint8Array(32);for(window.crypto.getRandomValues(Ho),bt=0;bt<32;++bt)ut[je++]=Ho[bt]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var Vo=window.crypto.random(32);for(bt=0;bt<Vo.length;++bt)ut[je++]=Vo.charCodeAt(bt)&255}}for(;je<Bn;)bt=Math.floor(65536*Math.random()),ut[je++]=bt>>>8,ut[je++]=bt&255;je=0,qo()}function Vh(){if(Fr==null){for(qo(),Fr=Wh(),Fr.init(ut),je=0;je<ut.length;++je)ut[je]=0;je=0}return Fr.next()}function $h(a){var c;for(c=0;c<a.length;++c)a[c]=Vh()}function In(){}In.prototype.nextBytes=$h;function An(){this.i=0,this.j=0,this.S=new Array}function Gh(a){var c,p,g;for(c=0;c<256;++c)this.S[c]=c;for(p=0,c=0;c<256;++c)p=p+this.S[c]+a[c%a.length]&255,g=this.S[c],this.S[c]=this.S[p],this.S[p]=g;this.i=0,this.j=0}function zh(){var a;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,a=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=a,this.S[a+this.S[this.i]&255]}An.prototype.init=Gh,An.prototype.next=zh;function Wh(){return new An}var Bn=256;typeof rn!="undefined"?rn=au.exports={default:r,BigInteger:r,SecureRandom:In}:this.jsbn={BigInteger:r,SecureRandom:In}}).call(rn)});var hr=S(nn=>{(function(){"use strict";var t={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function e(o){return r(s(o),arguments)}function i(o,f){return e.apply(null,[o].concat(f||[]))}function r(o,f){var u=1,h=o.length,l,d="",m,_,v,x,b,T,B,E;for(m=0;m<h;m++)if(typeof o[m]=="string")d+=o[m];else if(typeof o[m]=="object"){if(v=o[m],v.keys)for(l=f[u],_=0;_<v.keys.length;_++){if(l==null)throw new Error(e('[sprintf] Cannot access property "%s" of undefined value "%s"',v.keys[_],v.keys[_-1]));l=l[v.keys[_]]}else v.param_no?l=f[v.param_no]:l=f[u++];if(t.not_type.test(v.type)&&t.not_primitive.test(v.type)&&l instanceof Function&&(l=l()),t.numeric_arg.test(v.type)&&typeof l!="number"&&isNaN(l))throw new TypeError(e("[sprintf] expecting number but found %T",l));switch(t.number.test(v.type)&&(B=l>=0),v.type){case"b":l=parseInt(l,10).toString(2);break;case"c":l=String.fromCharCode(parseInt(l,10));break;case"d":case"i":l=parseInt(l,10);break;case"j":l=JSON.stringify(l,null,v.width?parseInt(v.width):0);break;case"e":l=v.precision?parseFloat(l).toExponential(v.precision):parseFloat(l).toExponential();break;case"f":l=v.precision?parseFloat(l).toFixed(v.precision):parseFloat(l);break;case"g":l=v.precision?String(Number(l.toPrecision(v.precision))):parseFloat(l);break;case"o":l=(parseInt(l,10)>>>0).toString(8);break;case"s":l=String(l),l=v.precision?l.substring(0,v.precision):l;break;case"t":l=String(!!l),l=v.precision?l.substring(0,v.precision):l;break;case"T":l=Object.prototype.toString.call(l).slice(8,-1).toLowerCase(),l=v.precision?l.substring(0,v.precision):l;break;case"u":l=parseInt(l,10)>>>0;break;case"v":l=l.valueOf(),l=v.precision?l.substring(0,v.precision):l;break;case"x":l=(parseInt(l,10)>>>0).toString(16);break;case"X":l=(parseInt(l,10)>>>0).toString(16).toUpperCase();break}t.json.test(v.type)?d+=l:(t.number.test(v.type)&&(!B||v.sign)?(E=B?"+":"-",l=l.toString().replace(t.sign,"")):E="",b=v.pad_char?v.pad_char==="0"?"0":v.pad_char.charAt(1):" ",T=v.width-(E+l).length,x=v.width&&T>0?b.repeat(T):"",d+=v.align?E+l+x:b==="0"?E+x+l:x+E+l)}return d}var n=Object.create(null);function s(o){if(n[o])return n[o];for(var f=o,u,h=[],l=0;f;){if((u=t.text.exec(f))!==null)h.push(u[0]);else if((u=t.modulo.exec(f))!==null)h.push("%");else if((u=t.placeholder.exec(f))!==null){if(u[2]){l|=1;var d=[],m=u[2],_=[];if((_=t.key.exec(m))!==null)for(d.push(_[1]);(m=m.substring(_[0].length))!=="";)if((_=t.key_access.exec(m))!==null)d.push(_[1]);else if((_=t.index_access.exec(m))!==null)d.push(_[1]);else throw new SyntaxError("[sprintf] failed to parse named argument key");else throw new SyntaxError("[sprintf] failed to parse named argument key");u[2]=d}else l|=2;if(l===3)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");h.push({placeholder:u[0],param_no:u[1],keys:u[2],sign:u[3],pad_char:u[4],align:u[5],width:u[6],precision:u[7],type:u[8]})}else throw new SyntaxError("[sprintf] unexpected placeholder");f=f.substring(u[0].length)}return n[o]=h}typeof nn!="undefined"&&(nn.sprintf=e,nn.vsprintf=i),typeof window!="undefined"&&(window.sprintf=e,window.vsprintf=i,typeof define=="function"&&define.amd&&define(function(){return{sprintf:e,vsprintf:i}}))})()});var $s=S(Pt=>{"use strict";var S0=Pt&&Pt.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),E0=Pt&&Pt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),uu=Pt&&Pt.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&S0(e,t,i);return E0(e,t),e};Object.defineProperty(Pt,"__esModule",{value:!0});Pt.Address4=void 0;var lu=uu(Us()),Tt=uu(js()),cu=tn(),pr=Hs(),zi=hr(),Vs=class t{constructor(e){this.groups=Tt.GROUPS,this.parsedAddress=[],this.parsedSubnet="",this.subnet="/32",this.subnetMask=32,this.v4=!0,this.isCorrect=lu.isCorrect(Tt.BITS),this.isInSubnet=lu.isInSubnet,this.address=e;let i=Tt.RE_SUBNET_STRING.exec(e);if(i){if(this.parsedSubnet=i[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,this.subnetMask<0||this.subnetMask>Tt.BITS)throw new cu.AddressError("Invalid subnet mask.");e=e.replace(Tt.RE_SUBNET_STRING,"")}this.addressMinusSuffix=e,this.parsedAddress=this.parse(e)}static isValid(e){try{return new t(e),!0}catch{return!1}}parse(e){let i=e.split(".");if(!e.match(Tt.RE_ADDRESS))throw new cu.AddressError("Invalid IPv4 address.");return i}correctForm(){return this.parsedAddress.map(e=>parseInt(e,10)).join(".")}static fromHex(e){let i=e.replace(/:/g,"").padStart(8,"0"),r=[],n;for(n=0;n<8;n+=2){let s=i.slice(n,n+2);r.push(parseInt(s,16))}return new t(r.join("."))}static fromInteger(e){return t.fromHex(e.toString(16))}static fromArpa(e){let r=e.replace(/(\.in-addr\.arpa)?\.$/,"").split(".").reverse().join(".");return new t(r)}toHex(){return this.parsedAddress.map(e=>(0,zi.sprintf)("%02x",parseInt(e,10))).join(":")}toArray(){return this.parsedAddress.map(e=>parseInt(e,10))}toGroup6(){let e=[],i;for(i=0;i<Tt.GROUPS;i+=2){let r=(0,zi.sprintf)("%02x%02x",parseInt(this.parsedAddress[i],10),parseInt(this.parsedAddress[i+1],10));e.push((0,zi.sprintf)("%x",parseInt(r,16)))}return e.join(":")}bigInteger(){return new pr.BigInteger(this.parsedAddress.map(e=>(0,zi.sprintf)("%02x",parseInt(e,10))).join(""),16)}_startAddress(){return new pr.BigInteger(this.mask()+"0".repeat(Tt.BITS-this.subnetMask),2)}startAddress(){return t.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new pr.BigInteger("1");return t.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new pr.BigInteger(this.mask()+"1".repeat(Tt.BITS-this.subnetMask),2)}endAddress(){return t.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new pr.BigInteger("1");return t.fromBigInteger(this._endAddress().subtract(e))}static fromBigInteger(e){return t.fromInteger(parseInt(e.toString(),10))}mask(e){return e===void 0&&(e=this.subnetMask),this.getBitsBase2(0,e)}getBitsBase2(e,i){return this.binaryZeroPad().slice(e,i)}reverseForm(e){e||(e={});let i=this.correctForm().split(".").reverse().join(".");return e.omitSuffix?i:(0,zi.sprintf)("%s.in-addr.arpa.",i)}isMulticast(){return this.isInSubnet(new t("*********/4"))}binaryZeroPad(){return this.bigInteger().toString(2).padStart(Tt.BITS,"0")}groupForV6(){let e=this.parsedAddress;return this.address.replace(Tt.RE_ADDRESS,(0,zi.sprintf)('<span class="hover-group group-v4 group-6">%s</span>.<span class="hover-group group-v4 group-7">%s</span>',e.slice(0,2).join("."),e.slice(2,4).join(".")))}};Pt.Address4=Vs});var Gs=S(Ne=>{"use strict";Object.defineProperty(Ne,"__esModule",{value:!0});Ne.RE_URL_WITH_PORT=Ne.RE_URL=Ne.RE_ZONE_STRING=Ne.RE_SUBNET_STRING=Ne.RE_BAD_ADDRESS=Ne.RE_BAD_CHARACTERS=Ne.TYPES=Ne.SCOPES=Ne.GROUPS=Ne.BITS=void 0;Ne.BITS=128;Ne.GROUPS=8;Ne.SCOPES={0:"Reserved",1:"Interface local",2:"Link local",4:"Admin local",5:"Site local",8:"Organization local",14:"Global",15:"Reserved"};Ne.TYPES={"ff01::1/128":"Multicast (All nodes on this interface)","ff01::2/128":"Multicast (All routers on this interface)","ff02::1/128":"Multicast (All nodes on this link)","ff02::2/128":"Multicast (All routers on this link)","ff05::2/128":"Multicast (All routers in this site)","ff02::5/128":"Multicast (OSPFv3 AllSPF routers)","ff02::6/128":"Multicast (OSPFv3 AllDR routers)","ff02::9/128":"Multicast (RIP routers)","ff02::a/128":"Multicast (EIGRP routers)","ff02::d/128":"Multicast (PIM routers)","ff02::16/128":"Multicast (MLDv2 reports)","ff01::fb/128":"Multicast (mDNSv6)","ff02::fb/128":"Multicast (mDNSv6)","ff05::fb/128":"Multicast (mDNSv6)","ff02::1:2/128":"Multicast (All DHCP servers and relay agents on this link)","ff05::1:2/128":"Multicast (All DHCP servers and relay agents in this site)","ff02::1:3/128":"Multicast (All DHCP servers on this link)","ff05::1:3/128":"Multicast (All DHCP servers in this site)","::/128":"Unspecified","::1/128":"Loopback","ff00::/8":"Multicast","fe80::/10":"Link-local unicast"};Ne.RE_BAD_CHARACTERS=/([^0-9a-f:/%])/gi;Ne.RE_BAD_ADDRESS=/([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\/$)/gi;Ne.RE_SUBNET_STRING=/\/\d{1,3}(?=%|$)/;Ne.RE_ZONE_STRING=/%.*$/;Ne.RE_URL=new RegExp(/^\[{0,1}([0-9a-f:]+)\]{0,1}/);Ne.RE_URL_WITH_PORT=new RegExp(/\[([0-9a-f:]+)\]:([0-9]{1,5})/)});var zs=S(Lt=>{"use strict";Object.defineProperty(Lt,"__esModule",{value:!0});Lt.simpleGroup=Lt.spanLeadingZeroes=Lt.spanAll=Lt.spanAllZeroes=void 0;var fu=hr();function hu(t){return t.replace(/(0+)/g,'<span class="zero">$1</span>')}Lt.spanAllZeroes=hu;function k0(t,e=0){return t.split("").map((r,n)=>(0,fu.sprintf)('<span class="digit value-%s position-%d">%s</span>',r,n+e,hu(r))).join("")}Lt.spanAll=k0;function pu(t){return t.replace(/^(0+)/,'<span class="zero">$1</span>')}function C0(t){return t.split(":").map(i=>pu(i)).join(":")}Lt.spanLeadingZeroes=C0;function O0(t,e=0){return t.split(":").map((r,n)=>/group-v4/.test(r)?r:(0,fu.sprintf)('<span class="hover-group group-%d">%s</span>',n+e,pu(r)))}Lt.simpleGroup=O0});var du=S($e=>{"use strict";var T0=$e&&$e.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),I0=$e&&$e.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),A0=$e&&$e.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&T0(e,t,i);return I0(e,t),e};Object.defineProperty($e,"__esModule",{value:!0});$e.possibleElisions=$e.simpleRegularExpression=$e.ADDRESS_BOUNDARY=$e.padGroup=$e.groupPossibilities=void 0;var B0=A0(Gs()),Wi=hr();function on(t){return(0,Wi.sprintf)("(%s)",t.join("|"))}$e.groupPossibilities=on;function sn(t){return t.length<4?(0,Wi.sprintf)("0{0,%d}%s",4-t.length,t):t}$e.padGroup=sn;$e.ADDRESS_BOUNDARY="[^A-Fa-f0-9:]";function R0(t){let e=[];t.forEach((r,n)=>{parseInt(r,16)===0&&e.push(n)});let i=e.map(r=>t.map((n,s)=>{if(s===r){let o=s===0||s===B0.GROUPS-1?":":"";return on([sn(n),o])}return sn(n)}).join(":"));return i.push(t.map(sn).join(":")),on(i)}$e.simpleRegularExpression=R0;function P0(t,e,i){let r=e?"":":",n=i?"":":",s=[];!e&&!i&&s.push("::"),e&&i&&s.push(""),(i&&!e||!i&&e)&&s.push(":"),s.push((0,Wi.sprintf)("%s(:0{1,4}){1,%d}",r,t-1)),s.push((0,Wi.sprintf)("(0{1,4}:){1,%d}%s",t-1,n)),s.push((0,Wi.sprintf)("(0{1,4}:){%d}0{1,4}",t-1));for(let o=1;o<t-1;o++)for(let f=1;f<t-o;f++)s.push((0,Wi.sprintf)("(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}",f,t-f-o-1));return on(s)}$e.possibleElisions=P0});var _u=S(Nt=>{"use strict";var L0=Nt&&Nt.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),N0=Nt&&Nt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),ln=Nt&&Nt.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&L0(e,t,i);return N0(e,t),e};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.Address6=void 0;var mu=ln(Us()),Ws=ln(js()),Ae=ln(Gs()),Ys=ln(zs()),vi=$s(),_i=du(),qt=tn(),rt=Hs(),nt=hr();function an(t){if(!t)throw new Error("Assertion failed.")}function F0(t){let e=/(\d+)(\d{3})/;for(;e.test(t);)t=t.replace(e,"$1,$2");return t}function M0(t){return t=t.replace(/^(0{1,})([1-9]+)$/,'<span class="parse-error">$1</span>$2'),t=t.replace(/^(0{1,})(0)$/,'<span class="parse-error">$1</span>$2'),t}function D0(t,e){let i=[],r=[],n;for(n=0;n<t.length;n++)n<e[0]?i.push(t[n]):n>e[1]&&r.push(t[n]);return i.concat(["compact"]).concat(r)}function gu(t){return(0,nt.sprintf)("%04x",parseInt(t,16))}function vu(t){return t&255}var Ks=class t{constructor(e,i){this.addressMinusSuffix="",this.parsedSubnet="",this.subnet="/128",this.subnetMask=128,this.v4=!1,this.zone="",this.isInSubnet=mu.isInSubnet,this.isCorrect=mu.isCorrect(Ae.BITS),i===void 0?this.groups=Ae.GROUPS:this.groups=i,this.address=e;let r=Ae.RE_SUBNET_STRING.exec(e);if(r){if(this.parsedSubnet=r[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,Number.isNaN(this.subnetMask)||this.subnetMask<0||this.subnetMask>Ae.BITS)throw new qt.AddressError("Invalid subnet mask.");e=e.replace(Ae.RE_SUBNET_STRING,"")}else if(/\//.test(e))throw new qt.AddressError("Invalid subnet mask.");let n=Ae.RE_ZONE_STRING.exec(e);n&&(this.zone=n[0],e=e.replace(Ae.RE_ZONE_STRING,"")),this.addressMinusSuffix=e,this.parsedAddress=this.parse(this.addressMinusSuffix)}static isValid(e){try{return new t(e),!0}catch{return!1}}static fromBigInteger(e){let i=e.toString(16).padStart(32,"0"),r=[],n;for(n=0;n<Ae.GROUPS;n++)r.push(i.slice(n*4,(n+1)*4));return new t(r.join(":"))}static fromURL(e){let i,r=null,n;if(e.indexOf("[")!==-1&&e.indexOf("]:")!==-1){if(n=Ae.RE_URL_WITH_PORT.exec(e),n===null)return{error:"failed to parse address with port",address:null,port:null};i=n[1],r=n[2]}else if(e.indexOf("/")!==-1){if(e=e.replace(/^[a-z0-9]+:\/\//,""),n=Ae.RE_URL.exec(e),n===null)return{error:"failed to parse address from URL",address:null,port:null};i=n[1]}else i=e;return r?(r=parseInt(r,10),(r<0||r>65536)&&(r=null)):r=null,{address:new t(i),port:r}}static fromAddress4(e){let i=new vi.Address4(e),r=Ae.BITS-(Ws.BITS-i.subnetMask);return new t(`::ffff:${i.correctForm()}/${r}`)}static fromArpa(e){let i=e.replace(/(\.ip6\.arpa)?\.$/,""),r=7;if(i.length!==63)throw new qt.AddressError("Invalid 'ip6.arpa' form.");let n=i.split(".").reverse();for(let s=r;s>0;s--){let o=s*4;n.splice(o,0,":")}return i=n.join(""),new t(i)}microsoftTranscription(){return(0,nt.sprintf)("%s.ipv6-literal.net",this.correctForm().replace(/:/g,"-"))}mask(e=this.subnetMask){return this.getBitsBase2(0,e)}possibleSubnets(e=128){let i=Ae.BITS-this.subnetMask,r=Math.abs(e-Ae.BITS),n=i-r;return n<0?"0":F0(new rt.BigInteger("2",10).pow(n).toString(10))}_startAddress(){return new rt.BigInteger(this.mask()+"0".repeat(Ae.BITS-this.subnetMask),2)}startAddress(){return t.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new rt.BigInteger("1");return t.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new rt.BigInteger(this.mask()+"1".repeat(Ae.BITS-this.subnetMask),2)}endAddress(){return t.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new rt.BigInteger("1");return t.fromBigInteger(this._endAddress().subtract(e))}getScope(){let e=Ae.SCOPES[this.getBits(12,16).intValue()];return this.getType()==="Global unicast"&&e!=="Link local"&&(e="Global"),e||"Unknown"}getType(){for(let e of Object.keys(Ae.TYPES))if(this.isInSubnet(new t(e)))return Ae.TYPES[e];return"Global unicast"}getBits(e,i){return new rt.BigInteger(this.getBitsBase2(e,i),2)}getBitsBase2(e,i){return this.binaryZeroPad().slice(e,i)}getBitsBase16(e,i){let r=i-e;if(r%4!==0)throw new Error("Length of bits to retrieve must be divisible by four");return this.getBits(e,i).toString(16).padStart(r/4,"0")}getBitsPastSubnet(){return this.getBitsBase2(this.subnetMask,Ae.BITS)}reverseForm(e){e||(e={});let i=Math.floor(this.subnetMask/4),r=this.canonicalForm().replace(/:/g,"").split("").slice(0,i).reverse().join(".");return i>0?e.omitSuffix?r:(0,nt.sprintf)("%s.ip6.arpa.",r):e.omitSuffix?"":"ip6.arpa."}correctForm(){let e,i=[],r=0,n=[];for(e=0;e<this.parsedAddress.length;e++){let f=parseInt(this.parsedAddress[e],16);f===0&&r++,f!==0&&r>0&&(r>1&&n.push([e-r,e-1]),r=0)}r>1&&n.push([this.parsedAddress.length-r,this.parsedAddress.length-1]);let s=n.map(f=>f[1]-f[0]+1);if(n.length>0){let f=s.indexOf(Math.max(...s));i=D0(this.parsedAddress,n[f])}else i=this.parsedAddress;for(e=0;e<i.length;e++)i[e]!=="compact"&&(i[e]=parseInt(i[e],16).toString(16));let o=i.join(":");return o=o.replace(/^compact$/,"::"),o=o.replace(/^compact|compact$/,":"),o=o.replace(/compact/,""),o}binaryZeroPad(){return this.bigInteger().toString(2).padStart(Ae.BITS,"0")}parse4in6(e){let i=e.split(":"),n=i.slice(-1)[0].match(Ws.RE_ADDRESS);if(n){this.parsedAddress4=n[0],this.address4=new vi.Address4(this.parsedAddress4);for(let s=0;s<this.address4.groups;s++)if(/^0[0-9]+/.test(this.address4.parsedAddress[s]))throw new qt.AddressError("IPv4 addresses can't have leading zeroes.",e.replace(Ws.RE_ADDRESS,this.address4.parsedAddress.map(M0).join(".")));this.v4=!0,i[i.length-1]=this.address4.toGroup6(),e=i.join(":")}return e}parse(e){e=this.parse4in6(e);let i=e.match(Ae.RE_BAD_CHARACTERS);if(i)throw new qt.AddressError((0,nt.sprintf)("Bad character%s detected in address: %s",i.length>1?"s":"",i.join("")),e.replace(Ae.RE_BAD_CHARACTERS,'<span class="parse-error">$1</span>'));let r=e.match(Ae.RE_BAD_ADDRESS);if(r)throw new qt.AddressError((0,nt.sprintf)("Address failed regex: %s",r.join("")),e.replace(Ae.RE_BAD_ADDRESS,'<span class="parse-error">$1</span>'));let n=[],s=e.split("::");if(s.length===2){let o=s[0].split(":"),f=s[1].split(":");o.length===1&&o[0]===""&&(o=[]),f.length===1&&f[0]===""&&(f=[]);let u=this.groups-(o.length+f.length);if(!u)throw new qt.AddressError("Error parsing groups");this.elidedGroups=u,this.elisionBegin=o.length,this.elisionEnd=o.length+this.elidedGroups,n=n.concat(o);for(let h=0;h<u;h++)n.push("0");n=n.concat(f)}else if(s.length===1)n=e.split(":"),this.elidedGroups=0;else throw new qt.AddressError("Too many :: groups found");if(n=n.map(o=>(0,nt.sprintf)("%x",parseInt(o,16))),n.length!==this.groups)throw new qt.AddressError("Incorrect number of groups found");return n}canonicalForm(){return this.parsedAddress.map(gu).join(":")}decimal(){return this.parsedAddress.map(e=>(0,nt.sprintf)("%05d",parseInt(e,16))).join(":")}bigInteger(){return new rt.BigInteger(this.parsedAddress.map(gu).join(""),16)}to4(){let e=this.binaryZeroPad().split("");return vi.Address4.fromHex(new rt.BigInteger(e.slice(96,128).join(""),2).toString(16))}to4in6(){let e=this.to4(),r=new t(this.parsedAddress.slice(0,6).join(":"),6).correctForm(),n="";return/:$/.test(r)||(n=":"),r+n+e.address}inspectTeredo(){let e=this.getBitsBase16(0,32),i=this.getBits(80,96).xor(new rt.BigInteger("ffff",16)).toString(),r=vi.Address4.fromHex(this.getBitsBase16(32,64)),n=vi.Address4.fromHex(this.getBits(96,128).xor(new rt.BigInteger("ffffffff",16)).toString(16)),s=this.getBits(64,80),o=this.getBitsBase2(64,80),f=s.testBit(15),u=s.testBit(14),h=s.testBit(8),l=s.testBit(9),d=new rt.BigInteger(o.slice(2,6)+o.slice(8,16),2).toString(10);return{prefix:(0,nt.sprintf)("%s:%s",e.slice(0,4),e.slice(4,8)),server4:r.address,client4:n.address,flags:o,coneNat:f,microsoft:{reserved:u,universalLocal:l,groupIndividual:h,nonce:d},udpPort:i}}inspect6to4(){let e=this.getBitsBase16(0,16),i=vi.Address4.fromHex(this.getBitsBase16(16,48));return{prefix:(0,nt.sprintf)("%s",e.slice(0,4)),gateway:i.address}}to6to4(){if(!this.is4())return null;let e=["2002",this.getBitsBase16(96,112),this.getBitsBase16(112,128),"","/16"].join(":");return new t(e)}toByteArray(){let e=this.bigInteger().toByteArray();return e.length===17&&e[0]===0?e.slice(1):e}toUnsignedByteArray(){return this.toByteArray().map(vu)}static fromByteArray(e){return this.fromUnsignedByteArray(e.map(vu))}static fromUnsignedByteArray(e){let i=new rt.BigInteger("256",10),r=new rt.BigInteger("0",10),n=new rt.BigInteger("1",10);for(let s=e.length-1;s>=0;s--)r=r.add(n.multiply(new rt.BigInteger(e[s].toString(10),10))),n=n.multiply(i);return t.fromBigInteger(r)}isCanonical(){return this.addressMinusSuffix===this.canonicalForm()}isLinkLocal(){return this.getBitsBase2(0,64)==="1111111010000000000000000000000000000000000000000000000000000000"}isMulticast(){return this.getType()==="Multicast"}is4(){return this.v4}isTeredo(){return this.isInSubnet(new t("2001::/32"))}is6to4(){return this.isInSubnet(new t("2002::/16"))}isLoopback(){return this.getType()==="Loopback"}href(e){return e===void 0?e="":e=(0,nt.sprintf)(":%s",e),(0,nt.sprintf)("http://[%s]%s/",this.correctForm(),e)}link(e){e||(e={}),e.className===void 0&&(e.className=""),e.prefix===void 0&&(e.prefix="/#address="),e.v4===void 0&&(e.v4=!1);let i=this.correctForm;return e.v4&&(i=this.to4in6),e.className?(0,nt.sprintf)('<a href="%1$s%2$s" class="%3$s">%2$s</a>',e.prefix,i.call(this),e.className):(0,nt.sprintf)('<a href="%1$s%2$s">%2$s</a>',e.prefix,i.call(this))}group(){if(this.elidedGroups===0)return Ys.simpleGroup(this.address).join(":");an(typeof this.elidedGroups=="number"),an(typeof this.elisionBegin=="number");let e=[],[i,r]=this.address.split("::");i.length?e.push(...Ys.simpleGroup(i)):e.push("");let n=["hover-group"];for(let s=this.elisionBegin;s<this.elisionBegin+this.elidedGroups;s++)n.push((0,nt.sprintf)("group-%d",s));return e.push((0,nt.sprintf)('<span class="%s"></span>',n.join(" "))),r.length?e.push(...Ys.simpleGroup(r,this.elisionEnd)):e.push(""),this.is4()&&(an(this.address4 instanceof vi.Address4),e.pop(),e.push(this.address4.groupForV6())),e.join(":")}regularExpressionString(e=!1){let i=[],r=new t(this.correctForm());if(r.elidedGroups===0)i.push((0,_i.simpleRegularExpression)(r.parsedAddress));else if(r.elidedGroups===Ae.GROUPS)i.push((0,_i.possibleElisions)(Ae.GROUPS));else{let n=r.address.split("::");n[0].length&&i.push((0,_i.simpleRegularExpression)(n[0].split(":"))),an(typeof r.elidedGroups=="number"),i.push((0,_i.possibleElisions)(r.elidedGroups,n[0].length!==0,n[1].length!==0)),n[1].length&&i.push((0,_i.simpleRegularExpression)(n[1].split(":"))),i=[i.join(":")]}return e||(i=["(?=^|",_i.ADDRESS_BOUNDARY,"|[^\\w\\:])(",...i,")(?=[^\\w\\:]|",_i.ADDRESS_BOUNDARY,"|$)"]),i.join("")}regularExpression(e=!1){return new RegExp(this.regularExpressionString(e),"i")}};Nt.Address6=Ks});var Zs=S(Je=>{"use strict";var U0=Je&&Je.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),j0=Je&&Je.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),q0=Je&&Je.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&U0(e,t,i);return j0(e,t),e};Object.defineProperty(Je,"__esModule",{value:!0});Je.v6=Je.AddressError=Je.Address6=Je.Address4=void 0;var H0=$s();Object.defineProperty(Je,"Address4",{enumerable:!0,get:function(){return H0.Address4}});var V0=_u();Object.defineProperty(Je,"Address6",{enumerable:!0,get:function(){return V0.Address6}});var $0=tn();Object.defineProperty(Je,"AddressError",{enumerable:!0,get:function(){return $0.AddressError}});var G0=q0(zs());Je.v6={helpers:G0}});var Eu=S(_t=>{"use strict";Object.defineProperty(_t,"__esModule",{value:!0});_t.ipToBuffer=_t.int32ToIpv4=_t.ipv4ToInt32=_t.validateSocksClientChainOptions=_t.validateSocksClientOptions=void 0;var st=Ds(),Ge=Fs(),z0=require("stream"),Xs=Zs(),xu=require("net");function W0(t,e=["connect","bind","associate"]){if(!Ge.SocksCommand[t.command])throw new st.SocksClientError(Ge.ERRORS.InvalidSocksCommand,t);if(e.indexOf(t.command)===-1)throw new st.SocksClientError(Ge.ERRORS.InvalidSocksCommandForOperation,t);if(!bu(t.destination))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsDestination,t);if(!wu(t.proxy))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsProxy,t);if(yu(t.proxy,t),t.timeout&&!Su(t.timeout))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsTimeout,t);if(t.existing_socket&&!(t.existing_socket instanceof z0.Duplex))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsExistingSocket,t)}_t.validateSocksClientOptions=W0;function Y0(t){if(t.command!=="connect")throw new st.SocksClientError(Ge.ERRORS.InvalidSocksCommandChain,t);if(!bu(t.destination))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsDestination,t);if(!(t.proxies&&Array.isArray(t.proxies)&&t.proxies.length>=2))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsProxiesLength,t);if(t.proxies.forEach(e=>{if(!wu(e))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsProxy,t);yu(e,t)}),t.timeout&&!Su(t.timeout))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsTimeout,t)}_t.validateSocksClientChainOptions=Y0;function yu(t,e){if(t.custom_auth_method!==void 0){if(t.custom_auth_method<Ge.SOCKS5_CUSTOM_AUTH_START||t.custom_auth_method>Ge.SOCKS5_CUSTOM_AUTH_END)throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthRange,e);if(t.custom_auth_request_handler===void 0||typeof t.custom_auth_request_handler!="function")throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_size===void 0)throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_handler===void 0||typeof t.custom_auth_response_handler!="function")throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e)}}function bu(t){return t&&typeof t.host=="string"&&typeof t.port=="number"&&t.port>=0&&t.port<=65535}function wu(t){return t&&(typeof t.host=="string"||typeof t.ipaddress=="string")&&typeof t.port=="number"&&t.port>=0&&t.port<=65535&&(t.type===4||t.type===5)}function Su(t){return typeof t=="number"&&t>0}function K0(t){return new Xs.Address4(t).toArray().reduce((i,r)=>(i<<8)+r,0)}_t.ipv4ToInt32=K0;function Z0(t){let e=t>>>24&255,i=t>>>16&255,r=t>>>8&255,n=t&255;return[e,i,r,n].join(".")}_t.int32ToIpv4=Z0;function X0(t){if(xu.isIPv4(t)){let e=new Xs.Address4(t);return Buffer.from(e.toArray())}else if(xu.isIPv6(t)){let e=new Xs.Address6(t);return Buffer.from(e.canonicalForm().split(":").map(i=>i.padStart(4,"0")).join(""),"hex")}else throw new Error("Invalid IP address format")}_t.ipToBuffer=X0});var ku=S(cn=>{"use strict";Object.defineProperty(cn,"__esModule",{value:!0});cn.ReceiveBuffer=void 0;var Js=class{constructor(e=4096){this.buffer=Buffer.allocUnsafe(e),this.offset=0,this.originalSize=e}get length(){return this.offset}append(e){if(!Buffer.isBuffer(e))throw new Error("Attempted to append a non-buffer instance to ReceiveBuffer.");if(this.offset+e.length>=this.buffer.length){let i=this.buffer;this.buffer=Buffer.allocUnsafe(Math.max(this.buffer.length+this.originalSize,this.buffer.length+e.length)),i.copy(this.buffer)}return e.copy(this.buffer,this.offset),this.offset+=e.length}peek(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");return this.buffer.slice(0,e)}get(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");let i=Buffer.allocUnsafe(e);return this.buffer.slice(0,e).copy(i),this.buffer.copyWithin(0,e,e+this.offset-e),this.offset-=e,i}};cn.ReceiveBuffer=Js});var Cu=S(Xt=>{"use strict";var Yi=Xt&&Xt.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function f(l){try{h(r.next(l))}catch(d){o(d)}}function u(l){try{h(r.throw(l))}catch(d){o(d)}}function h(l){l.done?s(l.value):n(l.value).then(f,u)}h((r=r.apply(t,e||[])).next())})};Object.defineProperty(Xt,"__esModule",{value:!0});Xt.SocksClientError=Xt.SocksClient=void 0;var J0=require("events"),Ki=require("net"),lt=eu(),P=Fs(),dt=Eu(),Q0=ku(),eo=Ds();Object.defineProperty(Xt,"SocksClientError",{enumerable:!0,get:function(){return eo.SocksClientError}});var Qs=Zs(),to=class t extends J0.EventEmitter{constructor(e){super(),this.options=Object.assign({},e),(0,dt.validateSocksClientOptions)(e),this.setState(P.SocksClientState.Created)}static createConnection(e,i){return new Promise((r,n)=>{try{(0,dt.validateSocksClientOptions)(e,["connect"])}catch(o){return typeof i=="function"?(i(o),r(o)):n(o)}let s=new t(e);s.connect(e.existing_socket),s.once("established",o=>{s.removeAllListeners(),typeof i=="function"&&i(null,o),r(o)}),s.once("error",o=>{s.removeAllListeners(),typeof i=="function"?(i(o),r(o)):n(o)})})}static createConnectionChain(e,i){return new Promise((r,n)=>Yi(this,void 0,void 0,function*(){try{(0,dt.validateSocksClientChainOptions)(e)}catch(s){return typeof i=="function"?(i(s),r(s)):n(s)}e.randomizeChain&&(0,eo.shuffleArray)(e.proxies);try{let s;for(let o=0;o<e.proxies.length;o++){let f=e.proxies[o],u=o===e.proxies.length-1?e.destination:{host:e.proxies[o+1].host||e.proxies[o+1].ipaddress,port:e.proxies[o+1].port},h=yield t.createConnection({command:"connect",proxy:f,destination:u,existing_socket:s});s=s||h.socket}typeof i=="function"?(i(null,{socket:s}),r({socket:s})):r({socket:s})}catch(s){typeof i=="function"?(i(s),r(s)):n(s)}}))}static createUDPFrame(e){let i=new lt.SmartBuffer;return i.writeUInt16BE(0),i.writeUInt8(e.frameNumber||0),Ki.isIPv4(e.remoteHost.host)?(i.writeUInt8(P.Socks5HostType.IPv4),i.writeUInt32BE((0,dt.ipv4ToInt32)(e.remoteHost.host))):Ki.isIPv6(e.remoteHost.host)?(i.writeUInt8(P.Socks5HostType.IPv6),i.writeBuffer((0,dt.ipToBuffer)(e.remoteHost.host))):(i.writeUInt8(P.Socks5HostType.Hostname),i.writeUInt8(Buffer.byteLength(e.remoteHost.host)),i.writeString(e.remoteHost.host)),i.writeUInt16BE(e.remoteHost.port),i.writeBuffer(e.data),i.toBuffer()}static parseUDPFrame(e){let i=lt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r=i.readUInt8(),n=i.readUInt8(),s;n===P.Socks5HostType.IPv4?s=(0,dt.int32ToIpv4)(i.readUInt32BE()):n===P.Socks5HostType.IPv6?s=Qs.Address6.fromByteArray(Array.from(i.readBuffer(16))).canonicalForm():s=i.readString(i.readUInt8());let o=i.readUInt16BE();return{frameNumber:r,remoteHost:{host:s,port:o},data:i.readBuffer()}}setState(e){this.state!==P.SocksClientState.Error&&(this.state=e)}connect(e){this.onDataReceived=r=>this.onDataReceivedHandler(r),this.onClose=()=>this.onCloseHandler(),this.onError=r=>this.onErrorHandler(r),this.onConnect=()=>this.onConnectHandler();let i=setTimeout(()=>this.onEstablishedTimeout(),this.options.timeout||P.DEFAULT_TIMEOUT);i.unref&&typeof i.unref=="function"&&i.unref(),e?this.socket=e:this.socket=new Ki.Socket,this.socket.once("close",this.onClose),this.socket.once("error",this.onError),this.socket.once("connect",this.onConnect),this.socket.on("data",this.onDataReceived),this.setState(P.SocksClientState.Connecting),this.receiveBuffer=new Q0.ReceiveBuffer,e?this.socket.emit("connect"):(this.socket.connect(this.getSocketOptions()),this.options.set_tcp_nodelay!==void 0&&this.options.set_tcp_nodelay!==null&&this.socket.setNoDelay(!!this.options.set_tcp_nodelay)),this.prependOnceListener("established",r=>{setImmediate(()=>{if(this.receiveBuffer.length>0){let n=this.receiveBuffer.get(this.receiveBuffer.length);r.socket.emit("data",n)}r.socket.resume()})})}getSocketOptions(){return Object.assign(Object.assign({},this.options.socket_options),{host:this.options.proxy.host||this.options.proxy.ipaddress,port:this.options.proxy.port})}onEstablishedTimeout(){this.state!==P.SocksClientState.Established&&this.state!==P.SocksClientState.BoundWaitingForConnection&&this.closeSocket(P.ERRORS.ProxyConnectionTimedOut)}onConnectHandler(){this.setState(P.SocksClientState.Connected),this.options.proxy.type===4?this.sendSocks4InitialHandshake():this.sendSocks5InitialHandshake(),this.setState(P.SocksClientState.SentInitialHandshake)}onDataReceivedHandler(e){this.receiveBuffer.append(e),this.processData()}processData(){for(;this.state!==P.SocksClientState.Established&&this.state!==P.SocksClientState.Error&&this.receiveBuffer.length>=this.nextRequiredPacketBufferSize;)if(this.state===P.SocksClientState.SentInitialHandshake)this.options.proxy.type===4?this.handleSocks4FinalHandshakeResponse():this.handleInitialSocks5HandshakeResponse();else if(this.state===P.SocksClientState.SentAuthentication)this.handleInitialSocks5AuthenticationHandshakeResponse();else if(this.state===P.SocksClientState.SentFinalHandshake)this.handleSocks5FinalHandshakeResponse();else if(this.state===P.SocksClientState.BoundWaitingForConnection)this.options.proxy.type===4?this.handleSocks4IncomingConnectionResponse():this.handleSocks5IncomingConnectionResponse();else{this.closeSocket(P.ERRORS.InternalError);break}}onCloseHandler(){this.closeSocket(P.ERRORS.SocketClosed)}onErrorHandler(e){this.closeSocket(e.message)}removeInternalSocketHandlers(){this.socket.pause(),this.socket.removeListener("data",this.onDataReceived),this.socket.removeListener("close",this.onClose),this.socket.removeListener("error",this.onError),this.socket.removeListener("connect",this.onConnect)}closeSocket(e){this.state!==P.SocksClientState.Error&&(this.setState(P.SocksClientState.Error),this.socket.destroy(),this.removeInternalSocketHandlers(),this.emit("error",new eo.SocksClientError(e,this.options)))}sendSocks4InitialHandshake(){let e=this.options.proxy.userId||"",i=new lt.SmartBuffer;i.writeUInt8(4),i.writeUInt8(P.SocksCommand[this.options.command]),i.writeUInt16BE(this.options.destination.port),Ki.isIPv4(this.options.destination.host)?(i.writeBuffer((0,dt.ipToBuffer)(this.options.destination.host)),i.writeStringNT(e)):(i.writeUInt8(0),i.writeUInt8(0),i.writeUInt8(0),i.writeUInt8(1),i.writeStringNT(e),i.writeStringNT(this.options.destination.host)),this.nextRequiredPacketBufferSize=P.SOCKS_INCOMING_PACKET_SIZES.Socks4Response,this.socket.write(i.toBuffer())}handleSocks4FinalHandshakeResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==P.Socks4Response.Granted)this.closeSocket(`${P.ERRORS.Socks4ProxyRejectedConnection} - (${P.Socks4Response[e[1]]})`);else if(P.SocksCommand[this.options.command]===P.SocksCommand.bind){let i=lt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r={port:i.readUInt16BE(),host:(0,dt.int32ToIpv4)(i.readUInt32BE())};r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress),this.setState(P.SocksClientState.BoundWaitingForConnection),this.emit("bound",{remoteHost:r,socket:this.socket})}else this.setState(P.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{socket:this.socket})}handleSocks4IncomingConnectionResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==P.Socks4Response.Granted)this.closeSocket(`${P.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${P.Socks4Response[e[1]]})`);else{let i=lt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r={port:i.readUInt16BE(),host:(0,dt.int32ToIpv4)(i.readUInt32BE())};this.setState(P.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}sendSocks5InitialHandshake(){let e=new lt.SmartBuffer,i=[P.Socks5Auth.NoAuth];(this.options.proxy.userId||this.options.proxy.password)&&i.push(P.Socks5Auth.UserPass),this.options.proxy.custom_auth_method!==void 0&&i.push(this.options.proxy.custom_auth_method),e.writeUInt8(5),e.writeUInt8(i.length);for(let r of i)e.writeUInt8(r);this.nextRequiredPacketBufferSize=P.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse,this.socket.write(e.toBuffer()),this.setState(P.SocksClientState.SentInitialHandshake)}handleInitialSocks5HandshakeResponse(){let e=this.receiveBuffer.get(2);e[0]!==5?this.closeSocket(P.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion):e[1]===P.SOCKS5_NO_ACCEPTABLE_AUTH?this.closeSocket(P.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType):e[1]===P.Socks5Auth.NoAuth?(this.socks5ChosenAuthType=P.Socks5Auth.NoAuth,this.sendSocks5CommandRequest()):e[1]===P.Socks5Auth.UserPass?(this.socks5ChosenAuthType=P.Socks5Auth.UserPass,this.sendSocks5UserPassAuthentication()):e[1]===this.options.proxy.custom_auth_method?(this.socks5ChosenAuthType=this.options.proxy.custom_auth_method,this.sendSocks5CustomAuthentication()):this.closeSocket(P.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType)}sendSocks5UserPassAuthentication(){let e=this.options.proxy.userId||"",i=this.options.proxy.password||"",r=new lt.SmartBuffer;r.writeUInt8(1),r.writeUInt8(Buffer.byteLength(e)),r.writeString(e),r.writeUInt8(Buffer.byteLength(i)),r.writeString(i),this.nextRequiredPacketBufferSize=P.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse,this.socket.write(r.toBuffer()),this.setState(P.SocksClientState.SentAuthentication)}sendSocks5CustomAuthentication(){return Yi(this,void 0,void 0,function*(){this.nextRequiredPacketBufferSize=this.options.proxy.custom_auth_response_size,this.socket.write(yield this.options.proxy.custom_auth_request_handler()),this.setState(P.SocksClientState.SentAuthentication)})}handleSocks5CustomAuthHandshakeResponse(e){return Yi(this,void 0,void 0,function*(){return yield this.options.proxy.custom_auth_response_handler(e)})}handleSocks5AuthenticationNoAuthHandshakeResponse(e){return Yi(this,void 0,void 0,function*(){return e[1]===0})}handleSocks5AuthenticationUserPassHandshakeResponse(e){return Yi(this,void 0,void 0,function*(){return e[1]===0})}handleInitialSocks5AuthenticationHandshakeResponse(){return Yi(this,void 0,void 0,function*(){this.setState(P.SocksClientState.ReceivedAuthenticationResponse);let e=!1;this.socks5ChosenAuthType===P.Socks5Auth.NoAuth?e=yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===P.Socks5Auth.UserPass?e=yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===this.options.proxy.custom_auth_method&&(e=yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size))),e?this.sendSocks5CommandRequest():this.closeSocket(P.ERRORS.Socks5AuthenticationFailed)})}sendSocks5CommandRequest(){let e=new lt.SmartBuffer;e.writeUInt8(5),e.writeUInt8(P.SocksCommand[this.options.command]),e.writeUInt8(0),Ki.isIPv4(this.options.destination.host)?(e.writeUInt8(P.Socks5HostType.IPv4),e.writeBuffer((0,dt.ipToBuffer)(this.options.destination.host))):Ki.isIPv6(this.options.destination.host)?(e.writeUInt8(P.Socks5HostType.IPv6),e.writeBuffer((0,dt.ipToBuffer)(this.options.destination.host))):(e.writeUInt8(P.Socks5HostType.Hostname),e.writeUInt8(this.options.destination.host.length),e.writeString(this.options.destination.host)),e.writeUInt16BE(this.options.destination.port),this.nextRequiredPacketBufferSize=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.socket.write(e.toBuffer()),this.setState(P.SocksClientState.SentFinalHandshake)}handleSocks5FinalHandshakeResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==P.Socks5Response.Granted)this.closeSocket(`${P.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${P.Socks5Response[e[1]]}`);else{let i=e[3],r,n;if(i===P.Socks5HostType.IPv4){let s=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,dt.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(i===P.Socks5HostType.Hostname){let s=e[4],o=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(i===P.Socks5HostType.IPv6){let s=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:Qs.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(P.SocksClientState.ReceivedFinalResponse),P.SocksCommand[this.options.command]===P.SocksCommand.connect?(this.setState(P.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})):P.SocksCommand[this.options.command]===P.SocksCommand.bind?(this.setState(P.SocksClientState.BoundWaitingForConnection),this.nextRequiredPacketBufferSize=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.emit("bound",{remoteHost:r,socket:this.socket})):P.SocksCommand[this.options.command]===P.SocksCommand.associate&&(this.setState(P.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket}))}}handleSocks5IncomingConnectionResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==P.Socks5Response.Granted)this.closeSocket(`${P.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${P.Socks5Response[e[1]]}`);else{let i=e[3],r,n;if(i===P.Socks5HostType.IPv4){let s=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,dt.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(i===P.Socks5HostType.Hostname){let s=e[4],o=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(i===P.Socks5HostType.IPv6){let s=P.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:Qs.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(P.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}get socksClientOptions(){return Object.assign({},this.options)}};Xt.SocksClient=to});var Ou=S(xi=>{"use strict";var eg=xi&&xi.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),tg=xi&&xi.__exportStar||function(t,e){for(var i in t)i!=="default"&&!Object.prototype.hasOwnProperty.call(e,i)&&eg(e,t,i)};Object.defineProperty(xi,"__esModule",{value:!0});tg(Cu(),xi)});var Tu=S(yi=>{"use strict";var ig=yi&&yi.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function f(l){try{h(r.next(l))}catch(d){o(d)}}function u(l){try{h(r.throw(l))}catch(d){o(d)}}function h(l){l.done?s(l.value):n(l.value).then(f,u)}h((r=r.apply(t,e||[])).next())})},un=yi&&yi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(yi,"__esModule",{value:!0});var rg=un(require("dns")),ng=un(require("tls")),sg=un(require("url")),og=un(Ni()),ag=jn(),lg=Ou(),io=og.default("socks-proxy-agent");function cg(t){return new Promise((e,i)=>{rg.default.lookup(t,(r,n)=>{r?i(r):e(n)})})}function ug(t){let e=0,i=!1,r=5,n=t.hostname||t.host;if(!n)throw new TypeError('No "host"');if(typeof t.port=="number"?e=t.port:typeof t.port=="string"&&(e=parseInt(t.port,10)),e||(e=1080),t.protocol)switch(t.protocol.replace(":","")){case"socks4":i=!0;case"socks4a":r=4;break;case"socks5":i=!0;case"socks":case"socks5h":r=5;break;default:throw new TypeError(`A "socks" protocol must be specified! Got: ${t.protocol}`)}if(typeof t.type!="undefined")if(t.type===4||t.type===5)r=t.type;else throw new TypeError(`"type" must be 4 or 5, got: ${t.type}`);let s={host:n,port:e,type:r},o=t.userId||t.username,f=t.password;if(t.auth){let u=t.auth.split(":");o=u[0],f=u[1]}return o&&Object.defineProperty(s,"userId",{value:o,enumerable:!1}),f&&Object.defineProperty(s,"password",{value:f,enumerable:!1}),{lookup:i,proxy:s}}var ro=class extends ag.Agent{constructor(e){let i;if(typeof e=="string"?i=sg.default.parse(e):i=e,!i)throw new TypeError("a SOCKS proxy server `host` and `port` must be specified!");super(i);let r=ug(i);this.lookup=r.lookup,this.proxy=r.proxy,this.tlsConnectionOptions=i.tls||{}}callback(e,i){return ig(this,void 0,void 0,function*(){let{lookup:r,proxy:n}=this,{host:s,port:o,timeout:f}=i;if(!s)throw new Error("No `host` defined!");r&&(s=yield cg(s));let u={proxy:n,destination:{host:s,port:o},command:"connect",timeout:f};io("Creating socks proxy connection: %o",u);let{socket:h}=yield lg.SocksClient.createConnection(u);if(io("Successfully created socks proxy connection"),i.secureEndpoint){io("Upgrading socket connection to TLS");let l=i.servername||i.host;return ng.default.connect(Object.assign(Object.assign(Object.assign({},fg(i,"host","hostname","path","port")),{socket:h,servername:l}),this.tlsConnectionOptions))}return h})}};yi.default=ro;function fg(t,...e){let i={},r;for(r in t)e.includes(r)||(i[r]=t[r]);return i}});var Au=S((oo,Iu)=>{"use strict";var hg=oo&&oo.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},no=hg(Tu());function so(t){return new no.default(t)}(function(t){t.SocksProxyAgent=no.default,t.prototype=no.default.prototype})(so||(so={}));Iu.exports=so});var Ru=S((Fx,Bu)=>{"use strict";var pg=/[|\\{}()[\]^$+*?.-]/g;Bu.exports=t=>{if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(pg,"\\$&")}});var Fu=S((Mx,Nu)=>{"use strict";var dg=Ru(),mg=typeof process=="object"&&process&&typeof process.cwd=="function"?process.cwd():".",Lu=[].concat(require("module").builtinModules,"bootstrap_node","node").map(t=>new RegExp(`(?:\\((?:node:)?${t}(?:\\.js)?:\\d+:\\d+\\)$|^\\s*at (?:node:)?${t}(?:\\.js)?:\\d+:\\d+$)`));Lu.push(/\((?:node:)?internal\/[^:]+:\d+:\d+\)$/,/\s*at (?:node:)?internal\/[^:]+:\d+:\d+$/,/\/\.node-spawn-wrap-\w+-\w+\/node:\d+:\d+\)?$/);var ao=class t{constructor(e){e={ignoredPackages:[],...e},"internals"in e||(e.internals=t.nodeInternals()),"cwd"in e||(e.cwd=mg),this._cwd=e.cwd.replace(/\\/g,"/"),this._internals=[].concat(e.internals,gg(e.ignoredPackages)),this._wrapCallSite=e.wrapCallSite||!1}static nodeInternals(){return[...Lu]}clean(e,i=0){i=" ".repeat(i),Array.isArray(e)||(e=e.split(`
`)),!/^\s*at /.test(e[0])&&/^\s*at /.test(e[1])&&(e=e.slice(1));let r=!1,n=null,s=[];return e.forEach(o=>{if(o=o.replace(/\\/g,"/"),this._internals.some(u=>u.test(o)))return;let f=/^\s*at /.test(o);r?o=o.trimEnd().replace(/^(\s+)at /,"$1"):(o=o.trim(),f&&(o=o.slice(3))),o=o.replace(`${this._cwd}/`,""),o&&(f?(n&&(s.push(n),n=null),s.push(o)):(r=!0,n=o))}),s.map(o=>`${i}${o}
`).join("")}captureString(e,i=this.captureString){typeof e=="function"&&(i=e,e=1/0);let{stackTraceLimit:r}=Error;e&&(Error.stackTraceLimit=e);let n={};Error.captureStackTrace(n,i);let{stack:s}=n;return Error.stackTraceLimit=r,this.clean(s)}capture(e,i=this.capture){typeof e=="function"&&(i=e,e=1/0);let{prepareStackTrace:r,stackTraceLimit:n}=Error;Error.prepareStackTrace=(f,u)=>this._wrapCallSite?u.map(this._wrapCallSite):u,e&&(Error.stackTraceLimit=e);let s={};Error.captureStackTrace(s,i);let{stack:o}=s;return Object.assign(Error,{prepareStackTrace:r,stackTraceLimit:n}),o}at(e=this.at){let[i]=this.capture(1,e);if(!i)return{};let r={line:i.getLineNumber(),column:i.getColumnNumber()};Pu(r,i.getFileName(),this._cwd),i.isConstructor()&&(r.constructor=!0),i.isEval()&&(r.evalOrigin=i.getEvalOrigin()),i.isNative()&&(r.native=!0);let n;try{n=i.getTypeName()}catch{}n&&n!=="Object"&&n!=="[object Object]"&&(r.type=n);let s=i.getFunctionName();s&&(r.function=s);let o=i.getMethodName();return o&&s!==o&&(r.method=o),r}parseLine(e){let i=e&&e.match(vg);if(!i)return null;let r=i[1]==="new",n=i[2],s=i[3],o=i[4],f=Number(i[5]),u=Number(i[6]),h=i[7],l=i[8],d=i[9],m=i[10]==="native",_=i[11]===")",v,x={};if(l&&(x.line=Number(l)),d&&(x.column=Number(d)),_&&h){let b=0;for(let T=h.length-1;T>0;T--)if(h.charAt(T)===")")b++;else if(h.charAt(T)==="("&&h.charAt(T-1)===" "&&(b--,b===-1&&h.charAt(T-1)===" ")){let B=h.slice(0,T-1);h=h.slice(T+1),n+=` (${B}`;break}}if(n){let b=n.match(_g);b&&(n=b[1],v=b[2])}return Pu(x,h,this._cwd),r&&(x.constructor=!0),s&&(x.evalOrigin=s,x.evalLine=f,x.evalColumn=u,x.evalFile=o&&o.replace(/\\/g,"/")),m&&(x.native=!0),n&&(x.function=n),v&&n!==v&&(x.method=v),x}};function Pu(t,e,i){e&&(e=e.replace(/\\/g,"/"),e.startsWith(`${i}/`)&&(e=e.slice(i.length+1)),t.file=e)}function gg(t){if(t.length===0)return[];let e=t.map(i=>dg(i));return new RegExp(`[/\\\\]node_modules[/\\\\](?:${e.join("|")})[/\\\\][^:]+:\\d+:\\d+`)}var vg=new RegExp("^(?:\\s*at )?(?:(new) )?(?:(.*?) \\()?(?:eval at ([^ ]+) \\((.+?):(\\d+):(\\d+)\\), )?(?:(.+?):(\\d+):(\\d+)|(native))(\\)?)$"),_g=/^(.*?) \[as (.*?)\]$/;Nu.exports=ao});var ju=S((Dx,Uu)=>{"use strict";var{Duplex:xg}=require("stream");function Mu(t){t.emit("close")}function yg(){!this.destroyed&&this._writableState.finished&&this.destroy()}function Du(t){this.removeListener("error",Du),this.destroy(),this.listenerCount("error")===0&&this.emit("error",t)}function bg(t,e){let i=!0,r=new xg({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return t.on("message",function(s,o){let f=!o&&r._readableState.objectMode?s.toString():s;r.push(f)||t.pause()}),t.once("error",function(s){r.destroyed||(i=!1,r.destroy(s))}),t.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(n,s){if(t.readyState===t.CLOSED){s(n),process.nextTick(Mu,r);return}let o=!1;t.once("error",function(u){o=!0,s(u)}),t.once("close",function(){o||s(n),process.nextTick(Mu,r)}),i&&t.terminate()},r._final=function(n){if(t.readyState===t.CONNECTING){t.once("open",function(){r._final(n)});return}t._socket!==null&&(t._socket._writableState.finished?(n(),r._readableState.endEmitted&&r.destroy()):(t._socket.once("finish",function(){n()}),t.close()))},r._read=function(){t.isPaused&&t.resume()},r._write=function(n,s,o){if(t.readyState===t.CONNECTING){t.once("open",function(){r._write(n,s,o)});return}t.send(n,o)},r.on("end",yg),r.on("error",Du),r}Uu.exports=bg});var Jt=S((Ux,qu)=>{"use strict";qu.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var dr=S((jx,lo)=>{"use strict";var{EMPTY_BUFFER:wg}=Jt();function Hu(t,e){if(t.length===0)return wg;if(t.length===1)return t[0];let i=Buffer.allocUnsafe(e),r=0;for(let n=0;n<t.length;n++){let s=t[n];i.set(s,r),r+=s.length}return r<e?i.slice(0,r):i}function Vu(t,e,i,r,n){for(let s=0;s<n;s++)i[r+s]=t[s]^e[s&3]}function $u(t,e){for(let i=0;i<t.length;i++)t[i]^=e[i&3]}function Gu(t){return t.byteLength===t.buffer.byteLength?t.buffer:t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)}function fn(t){if(fn.readOnly=!0,Buffer.isBuffer(t))return t;let e;return t instanceof ArrayBuffer?e=Buffer.from(t):ArrayBuffer.isView(t)?e=Buffer.from(t.buffer,t.byteOffset,t.byteLength):(e=Buffer.from(t),fn.readOnly=!1),e}try{let t=require("bufferutil");lo.exports={concat:Hu,mask(e,i,r,n,s){s<48?Vu(e,i,r,n,s):t.mask(e,i,r,n,s)},toArrayBuffer:Gu,toBuffer:fn,unmask(e,i){e.length<32?$u(e,i):t.unmask(e,i)}}}catch{lo.exports={concat:Hu,mask:Vu,toArrayBuffer:Gu,toBuffer:fn,unmask:$u}}});var Yu=S((qx,Wu)=>{"use strict";var zu=Symbol("kDone"),co=Symbol("kRun"),uo=class{constructor(e){this[zu]=()=>{this.pending--,this[co]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[co]()}[co](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[zu])}}};Wu.exports=uo});var vr=S((Hx,Ju)=>{"use strict";var mr=require("zlib"),Ku=dr(),Sg=Yu(),{kStatusCode:Zu}=Jt(),Eg=Buffer.from([0,0,255,255]),dn=Symbol("permessage-deflate"),Ht=Symbol("total-length"),gr=Symbol("callback"),Qt=Symbol("buffers"),pn=Symbol("error"),hn,fo=class{constructor(e,i,r){if(this._maxPayload=r|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!i,this._deflate=null,this._inflate=null,this.params=null,!hn){let n=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;hn=new Sg(n)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[gr];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let i=this._options,r=e.find(n=>!(i.serverNoContextTakeover===!1&&n.server_no_context_takeover||n.server_max_window_bits&&(i.serverMaxWindowBits===!1||typeof i.serverMaxWindowBits=="number"&&i.serverMaxWindowBits>n.server_max_window_bits)||typeof i.clientMaxWindowBits=="number"&&!n.client_max_window_bits));if(!r)throw new Error("None of the extension offers can be accepted");return i.serverNoContextTakeover&&(r.server_no_context_takeover=!0),i.clientNoContextTakeover&&(r.client_no_context_takeover=!0),typeof i.serverMaxWindowBits=="number"&&(r.server_max_window_bits=i.serverMaxWindowBits),typeof i.clientMaxWindowBits=="number"?r.client_max_window_bits=i.clientMaxWindowBits:(r.client_max_window_bits===!0||i.clientMaxWindowBits===!1)&&delete r.client_max_window_bits,r}acceptAsClient(e){let i=e[0];if(this._options.clientNoContextTakeover===!1&&i.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!i.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(i.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&i.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return i}normalizeParams(e){return e.forEach(i=>{Object.keys(i).forEach(r=>{let n=i[r];if(n.length>1)throw new Error(`Parameter "${r}" must have only a single value`);if(n=n[0],r==="client_max_window_bits"){if(n!==!0){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else if(r==="server_max_window_bits"){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(r==="client_no_context_takeover"||r==="server_no_context_takeover"){if(n!==!0)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else throw new Error(`Unknown parameter "${r}"`);i[r]=n})}),e}decompress(e,i,r){hn.add(n=>{this._decompress(e,i,(s,o)=>{n(),r(s,o)})})}compress(e,i,r){hn.add(n=>{this._compress(e,i,(s,o)=>{n(),r(s,o)})})}_decompress(e,i,r){let n=this._isServer?"client":"server";if(!this._inflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?mr.Z_DEFAULT_WINDOWBITS:this.params[s];this._inflate=mr.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[dn]=this,this._inflate[Ht]=0,this._inflate[Qt]=[],this._inflate.on("error",Cg),this._inflate.on("data",Xu)}this._inflate[gr]=r,this._inflate.write(e),i&&this._inflate.write(Eg),this._inflate.flush(()=>{let s=this._inflate[pn];if(s){this._inflate.close(),this._inflate=null,r(s);return}let o=Ku.concat(this._inflate[Qt],this._inflate[Ht]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[Ht]=0,this._inflate[Qt]=[],i&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,o)})}_compress(e,i,r){let n=this._isServer?"server":"client";if(!this._deflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?mr.Z_DEFAULT_WINDOWBITS:this.params[s];this._deflate=mr.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[Ht]=0,this._deflate[Qt]=[],this._deflate.on("data",kg)}this._deflate[gr]=r,this._deflate.write(e),this._deflate.flush(mr.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let s=Ku.concat(this._deflate[Qt],this._deflate[Ht]);i&&(s=s.slice(0,s.length-4)),this._deflate[gr]=null,this._deflate[Ht]=0,this._deflate[Qt]=[],i&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,s)})}};Ju.exports=fo;function kg(t){this[Qt].push(t),this[Ht]+=t.length}function Xu(t){if(this[Ht]+=t.length,this[dn]._maxPayload<1||this[Ht]<=this[dn]._maxPayload){this[Qt].push(t);return}this[pn]=new RangeError("Max payload size exceeded"),this[pn].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[pn][Zu]=1009,this.removeListener("data",Xu),this.reset()}function Cg(t){this[dn]._inflate=null,t[Zu]=1007,this[gr](t)}});var _r=S((Vx,ho)=>{"use strict";var Qu=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function ef(t){return t>=1e3&&t<=1014&&t!==1004&&t!==1005&&t!==1006||t>=3e3&&t<=4999}function tf(t){let e=t.length,i=0;for(;i<e;)if(!(t[i]&128))i++;else if((t[i]&224)===192){if(i+1===e||(t[i+1]&192)!==128||(t[i]&254)===192)return!1;i+=2}else if((t[i]&240)===224){if(i+2>=e||(t[i+1]&192)!==128||(t[i+2]&192)!==128||t[i]===224&&(t[i+1]&224)===128||t[i]===237&&(t[i+1]&224)===160)return!1;i+=3}else if((t[i]&248)===240){if(i+3>=e||(t[i+1]&192)!==128||(t[i+2]&192)!==128||(t[i+3]&192)!==128||t[i]===240&&(t[i+1]&240)===128||t[i]===244&&t[i+1]>143||t[i]>244)return!1;i+=4}else return!1;return!0}try{let t=require("utf-8-validate");ho.exports={isValidStatusCode:ef,isValidUTF8(e){return e.length<150?tf(e):t(e)},tokenChars:Qu}}catch{ho.exports={isValidStatusCode:ef,isValidUTF8:tf,tokenChars:Qu}}});var vo=S(($x,cf)=>{"use strict";var{Writable:Og}=require("stream"),rf=vr(),{BINARY_TYPES:Tg,EMPTY_BUFFER:nf,kStatusCode:Ig,kWebSocket:Ag}=Jt(),{concat:po,toArrayBuffer:Bg,unmask:Rg}=dr(),{isValidStatusCode:Pg,isValidUTF8:sf}=_r(),xr=0,of=1,af=2,lf=3,mo=4,Lg=5,go=class extends Og{constructor(e={}){super(),this._binaryType=e.binaryType||Tg[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[Ag]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._state=xr,this._loop=!1}_write(e,i,r){if(this._opcode===8&&this._state==xr)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let r=this._buffers[0];return this._buffers[0]=r.slice(e),r.slice(0,e)}let i=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=i.length-e;e>=r.length?i.set(this._buffers.shift(),n):(i.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=r.slice(e)),e-=r.length}while(e>0);return i}startLoop(e){let i;this._loop=!0;do switch(this._state){case xr:i=this.getInfo();break;case of:i=this.getPayloadLength16();break;case af:i=this.getPayloadLength64();break;case lf:this.getMask();break;case mo:i=this.getData(e);break;default:this._loop=!1;return}while(this._loop);e(i)}getInfo(){if(this._bufferedBytes<2){this._loop=!1;return}let e=this.consume(2);if(e[0]&48)return this._loop=!1,Ue(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");let i=(e[0]&64)===64;if(i&&!this._extensions[rf.extensionName])return this._loop=!1,Ue(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._fin=(e[0]&128)===128,this._opcode=e[0]&15,this._payloadLength=e[1]&127,this._opcode===0){if(i)return this._loop=!1,Ue(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(!this._fragmented)return this._loop=!1,Ue(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented)return this._loop=!1,Ue(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");this._compressed=i}else if(this._opcode>7&&this._opcode<11){if(!this._fin)return this._loop=!1,Ue(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");if(i)return this._loop=!1,Ue(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._payloadLength>125)return this._loop=!1,Ue(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH")}else return this._loop=!1,Ue(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(e[1]&128)===128,this._isServer){if(!this._masked)return this._loop=!1,Ue(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK")}else if(this._masked)return this._loop=!1,Ue(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");if(this._payloadLength===126)this._state=of;else if(this._payloadLength===127)this._state=af;else return this.haveLength()}getPayloadLength16(){if(this._bufferedBytes<2){this._loop=!1;return}return this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength()}getPayloadLength64(){if(this._bufferedBytes<8){this._loop=!1;return}let e=this.consume(8),i=e.readUInt32BE(0);return i>Math.pow(2,53-32)-1?(this._loop=!1,Ue(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH")):(this._payloadLength=i*Math.pow(2,32)+e.readUInt32BE(4),this.haveLength())}haveLength(){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return this._loop=!1,Ue(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");this._masked?this._state=lf:this._state=mo}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=mo}getData(e){let i=nf;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}i=this.consume(this._payloadLength),this._masked&&this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3]&&Rg(i,this._mask)}if(this._opcode>7)return this.controlMessage(i);if(this._compressed){this._state=Lg,this.decompress(i,e);return}return i.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(i)),this.dataMessage()}decompress(e,i){this._extensions[rf.extensionName].decompress(e,this._fin,(n,s)=>{if(n)return i(n);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return i(Ue(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(s)}let o=this.dataMessage();if(o)return i(o);this.startLoop(i)})}dataMessage(){if(this._fin){let e=this._messageLength,i=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let r;this._binaryType==="nodebuffer"?r=po(i,e):this._binaryType==="arraybuffer"?r=Bg(po(i,e)):r=i,this.emit("message",r,!0)}else{let r=po(i,e);if(!this._skipUTF8Validation&&!sf(r))return this._loop=!1,Ue(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("message",r,!1)}}this._state=xr}controlMessage(e){if(this._opcode===8)if(this._loop=!1,e.length===0)this.emit("conclude",1005,nf),this.end();else{if(e.length===1)return Ue(RangeError,"invalid payload length 1",!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");{let i=e.readUInt16BE(0);if(!Pg(i))return Ue(RangeError,`invalid status code ${i}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");let r=e.slice(2);if(!this._skipUTF8Validation&&!sf(r))return Ue(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("conclude",i,r),this.end()}}else this._opcode===9?this.emit("ping",e):this.emit("pong",e);this._state=xr}};cf.exports=go;function Ue(t,e,i,r,n){let s=new t(i?`Invalid WebSocket frame: ${e}`:e);return Error.captureStackTrace(s,Ue),s.code=n,s[Ig]=r,s}});var xo=S((Wx,hf)=>{"use strict";var Gx=require("net"),zx=require("tls"),{randomFillSync:Ng}=require("crypto"),uf=vr(),{EMPTY_BUFFER:Fg}=Jt(),{isValidStatusCode:Mg}=_r(),{mask:ff,toBuffer:Zi}=dr(),It=Symbol("kByteLength"),Dg=Buffer.alloc(4),_o=class t{constructor(e,i,r){this._extensions=i||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,i){let r,n=!1,s=2,o=!1;i.mask&&(r=i.maskBuffer||Dg,i.generateMask?i.generateMask(r):Ng(r,0,4),o=(r[0]|r[1]|r[2]|r[3])===0,s=6);let f;typeof e=="string"?(!i.mask||o)&&i[It]!==void 0?f=i[It]:(e=Buffer.from(e),f=e.length):(f=e.length,n=i.mask&&i.readOnly&&!o);let u=f;f>=65536?(s+=8,u=127):f>125&&(s+=2,u=126);let h=Buffer.allocUnsafe(n?f+s:s);return h[0]=i.fin?i.opcode|128:i.opcode,i.rsv1&&(h[0]|=64),h[1]=u,u===126?h.writeUInt16BE(f,2):u===127&&(h[2]=h[3]=0,h.writeUIntBE(f,4,6)),i.mask?(h[1]|=128,h[s-4]=r[0],h[s-3]=r[1],h[s-2]=r[2],h[s-1]=r[3],o?[h,e]:n?(ff(e,r,h,s,f),[h]):(ff(e,r,e,0,f),[h,e])):[h,e]}close(e,i,r,n){let s;if(e===void 0)s=Fg;else{if(typeof e!="number"||!Mg(e))throw new TypeError("First argument must be a valid error code number");if(i===void 0||!i.length)s=Buffer.allocUnsafe(2),s.writeUInt16BE(e,0);else{let f=Buffer.byteLength(i);if(f>123)throw new RangeError("The message must not be greater than 123 bytes");s=Buffer.allocUnsafe(2+f),s.writeUInt16BE(e,0),typeof i=="string"?s.write(i,2):s.set(i,2)}}let o={[It]:s.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,o,n]):this.sendFrame(t.frame(s,o),n)}ping(e,i,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=Zi(e),n=e.length,s=Zi.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[It]:n,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(t.frame(e,o),r)}pong(e,i,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=Zi(e),n=e.length,s=Zi.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[It]:n,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(t.frame(e,o),r)}send(e,i,r){let n=this._extensions[uf.extensionName],s=i.binary?2:1,o=i.compress,f,u;if(typeof e=="string"?(f=Buffer.byteLength(e),u=!1):(e=Zi(e),f=e.length,u=Zi.readOnly),this._firstFragment?(this._firstFragment=!1,o&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=f>=n._threshold),this._compress=o):(o=!1,s=0),i.fin&&(this._firstFragment=!0),n){let h={[It]:f,fin:i.fin,generateMask:this._generateMask,mask:i.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:u,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,h,r]):this.dispatch(e,this._compress,h,r)}else this.sendFrame(t.frame(e,{[It]:f,fin:i.fin,generateMask:this._generateMask,mask:i.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:u,rsv1:!1}),r)}dispatch(e,i,r,n){if(!i){this.sendFrame(t.frame(e,r),n);return}let s=this._extensions[uf.extensionName];this._bufferedBytes+=r[It],this._deflating=!0,s.compress(e,r.fin,(o,f)=>{if(this._socket.destroyed){let u=new Error("The socket was closed while data was being compressed");typeof n=="function"&&n(u);for(let h=0;h<this._queue.length;h++){let l=this._queue[h],d=l[l.length-1];typeof d=="function"&&d(u)}return}this._bufferedBytes-=r[It],this._deflating=!1,r.readOnly=!1,this.sendFrame(t.frame(f,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][It],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][It],this._queue.push(e)}sendFrame(e,i){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],i),this._socket.uncork()):this._socket.write(e[0],i)}};hf.exports=_o});var wf=S((Yx,bf)=>{"use strict";var{kForOnEventAttribute:yo,kListener:pf}=Jt(),df=Symbol("kCode"),mf=Symbol("kData"),gf=Symbol("kError"),vf=Symbol("kMessage"),_f=Symbol("kReason"),Xi=Symbol("kTarget"),xf=Symbol("kType"),yf=Symbol("kWasClean"),Vt=class{constructor(e){this[Xi]=null,this[xf]=e}get target(){return this[Xi]}get type(){return this[xf]}};Object.defineProperty(Vt.prototype,"target",{enumerable:!0});Object.defineProperty(Vt.prototype,"type",{enumerable:!0});var bi=class extends Vt{constructor(e,i={}){super(e),this[df]=i.code===void 0?0:i.code,this[_f]=i.reason===void 0?"":i.reason,this[yf]=i.wasClean===void 0?!1:i.wasClean}get code(){return this[df]}get reason(){return this[_f]}get wasClean(){return this[yf]}};Object.defineProperty(bi.prototype,"code",{enumerable:!0});Object.defineProperty(bi.prototype,"reason",{enumerable:!0});Object.defineProperty(bi.prototype,"wasClean",{enumerable:!0});var Ji=class extends Vt{constructor(e,i={}){super(e),this[gf]=i.error===void 0?null:i.error,this[vf]=i.message===void 0?"":i.message}get error(){return this[gf]}get message(){return this[vf]}};Object.defineProperty(Ji.prototype,"error",{enumerable:!0});Object.defineProperty(Ji.prototype,"message",{enumerable:!0});var yr=class extends Vt{constructor(e,i={}){super(e),this[mf]=i.data===void 0?null:i.data}get data(){return this[mf]}};Object.defineProperty(yr.prototype,"data",{enumerable:!0});var Ug={addEventListener(t,e,i={}){let r;if(t==="message")r=function(s,o){let f=new yr("message",{data:o?s:s.toString()});f[Xi]=this,e.call(this,f)};else if(t==="close")r=function(s,o){let f=new bi("close",{code:s,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});f[Xi]=this,e.call(this,f)};else if(t==="error")r=function(s){let o=new Ji("error",{error:s,message:s.message});o[Xi]=this,e.call(this,o)};else if(t==="open")r=function(){let s=new Vt("open");s[Xi]=this,e.call(this,s)};else return;r[yo]=!!i[yo],r[pf]=e,i.once?this.once(t,r):this.on(t,r)},removeEventListener(t,e){for(let i of this.listeners(t))if(i[pf]===e&&!i[yo]){this.removeListener(t,i);break}}};bf.exports={CloseEvent:bi,ErrorEvent:Ji,Event:Vt,EventTarget:Ug,MessageEvent:yr}});var bo=S((Kx,Sf)=>{"use strict";var{tokenChars:br}=_r();function Ft(t,e,i){t[e]===void 0?t[e]=[i]:t[e].push(i)}function jg(t){let e=Object.create(null),i=Object.create(null),r=!1,n=!1,s=!1,o,f,u=-1,h=-1,l=-1,d=0;for(;d<t.length;d++)if(h=t.charCodeAt(d),o===void 0)if(l===-1&&br[h]===1)u===-1&&(u=d);else if(d!==0&&(h===32||h===9))l===-1&&u!==-1&&(l=d);else if(h===59||h===44){if(u===-1)throw new SyntaxError(`Unexpected character at index ${d}`);l===-1&&(l=d);let _=t.slice(u,l);h===44?(Ft(e,_,i),i=Object.create(null)):o=_,u=l=-1}else throw new SyntaxError(`Unexpected character at index ${d}`);else if(f===void 0)if(l===-1&&br[h]===1)u===-1&&(u=d);else if(h===32||h===9)l===-1&&u!==-1&&(l=d);else if(h===59||h===44){if(u===-1)throw new SyntaxError(`Unexpected character at index ${d}`);l===-1&&(l=d),Ft(i,t.slice(u,l),!0),h===44&&(Ft(e,o,i),i=Object.create(null),o=void 0),u=l=-1}else if(h===61&&u!==-1&&l===-1)f=t.slice(u,d),u=l=-1;else throw new SyntaxError(`Unexpected character at index ${d}`);else if(n){if(br[h]!==1)throw new SyntaxError(`Unexpected character at index ${d}`);u===-1?u=d:r||(r=!0),n=!1}else if(s)if(br[h]===1)u===-1&&(u=d);else if(h===34&&u!==-1)s=!1,l=d;else if(h===92)n=!0;else throw new SyntaxError(`Unexpected character at index ${d}`);else if(h===34&&t.charCodeAt(d-1)===61)s=!0;else if(l===-1&&br[h]===1)u===-1&&(u=d);else if(u!==-1&&(h===32||h===9))l===-1&&(l=d);else if(h===59||h===44){if(u===-1)throw new SyntaxError(`Unexpected character at index ${d}`);l===-1&&(l=d);let _=t.slice(u,l);r&&(_=_.replace(/\\/g,""),r=!1),Ft(i,f,_),h===44&&(Ft(e,o,i),i=Object.create(null),o=void 0),f=void 0,u=l=-1}else throw new SyntaxError(`Unexpected character at index ${d}`);if(u===-1||s||h===32||h===9)throw new SyntaxError("Unexpected end of input");l===-1&&(l=d);let m=t.slice(u,l);return o===void 0?Ft(e,m,i):(f===void 0?Ft(i,m,!0):r?Ft(i,f,m.replace(/\\/g,"")):Ft(i,f,m),Ft(e,o,i)),e}function qg(t){return Object.keys(t).map(e=>{let i=t[e];return Array.isArray(i)||(i=[i]),i.map(r=>[e].concat(Object.keys(r).map(n=>{let s=r[n];return Array.isArray(s)||(s=[s]),s.map(o=>o===!0?n:`${n}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}Sf.exports={format:qg,parse:jg}});var Oo=S((Xx,Pf)=>{"use strict";var Hg=require("events"),Vg=require("https"),$g=require("http"),Cf=require("net"),Gg=require("tls"),{randomBytes:zg,createHash:Wg}=require("crypto"),{Readable:Zx}=require("stream"),{URL:wo}=require("url"),ei=vr(),Yg=vo(),Kg=xo(),{BINARY_TYPES:Ef,EMPTY_BUFFER:mn,GUID:Zg,kForOnEventAttribute:So,kListener:Xg,kStatusCode:Jg,kWebSocket:Qe,NOOP:Of}=Jt(),{EventTarget:{addEventListener:Qg,removeEventListener:ev}}=wf(),{format:tv,parse:iv}=bo(),{toBuffer:rv}=dr(),$t=["CONNECTING","OPEN","CLOSING","CLOSED"],nv=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,Eo=[8,13],sv=30*1e3,Fe=class t extends Hg{constructor(e,i,r){super(),this._binaryType=Ef[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=mn,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=t.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,i===void 0?i=[]:Array.isArray(i)||(typeof i=="object"&&i!==null?(r=i,i=[]):i=[i]),Tf(this,e,i,r)):this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(e){Ef.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,i,r){let n=new Yg({binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new Kg(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[Qe]=this,e[Qe]=this,n.on("conclude",lv),n.on("drain",cv),n.on("error",uv),n.on("message",fv),n.on("ping",hv),n.on("pong",pv),e.setTimeout(0),e.setNoDelay(),i.length>0&&e.unshift(i),e.on("close",Af),e.on("data",gn),e.on("end",Bf),e.on("error",Rf),this._readyState=t.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[ei.extensionName]&&this._extensions[ei.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,i){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){let r="WebSocket was closed before the connection was established";return xt(this,this._req,r)}if(this.readyState===t.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=t.CLOSING,this._sender.close(e,i,!this._isServer,r=>{r||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),sv)}}pause(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=i=void 0):typeof i=="function"&&(r=i,i=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Co(this,e,r);return}i===void 0&&(i=!this._isServer),this._sender.ping(e||mn,i,r)}pong(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=i=void 0):typeof i=="function"&&(r=i,i=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Co(this,e,r);return}i===void 0&&(i=!this._isServer),this._sender.pong(e||mn,i,r)}resume(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof i=="function"&&(r=i,i={}),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Co(this,e,r);return}let n={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...i};this._extensions[ei.extensionName]||(n.compress=!1),this._sender.send(e||mn,n,r)}terminate(){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){let e="WebSocket was closed before the connection was established";return xt(this,this._req,e)}this._socket&&(this._readyState=t.CLOSING,this._socket.destroy())}}};Object.defineProperty(Fe,"CONNECTING",{enumerable:!0,value:$t.indexOf("CONNECTING")});Object.defineProperty(Fe.prototype,"CONNECTING",{enumerable:!0,value:$t.indexOf("CONNECTING")});Object.defineProperty(Fe,"OPEN",{enumerable:!0,value:$t.indexOf("OPEN")});Object.defineProperty(Fe.prototype,"OPEN",{enumerable:!0,value:$t.indexOf("OPEN")});Object.defineProperty(Fe,"CLOSING",{enumerable:!0,value:$t.indexOf("CLOSING")});Object.defineProperty(Fe.prototype,"CLOSING",{enumerable:!0,value:$t.indexOf("CLOSING")});Object.defineProperty(Fe,"CLOSED",{enumerable:!0,value:$t.indexOf("CLOSED")});Object.defineProperty(Fe.prototype,"CLOSED",{enumerable:!0,value:$t.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(t=>{Object.defineProperty(Fe.prototype,t,{enumerable:!0})});["open","error","close","message"].forEach(t=>{Object.defineProperty(Fe.prototype,`on${t}`,{enumerable:!0,get(){for(let e of this.listeners(t))if(e[So])return e[Xg];return null},set(e){for(let i of this.listeners(t))if(i[So]){this.removeListener(t,i);break}typeof e=="function"&&this.addEventListener(t,e,{[So]:!0})}})});Fe.prototype.addEventListener=Qg;Fe.prototype.removeEventListener=ev;Pf.exports=Fe;function Tf(t,e,i,r){let n={protocolVersion:Eo[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...r,createConnection:void 0,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:void 0,host:void 0,path:void 0,port:void 0};if(!Eo.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${Eo.join(", ")})`);let s;if(e instanceof wo)s=e,t._url=e.href;else{try{s=new wo(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}t._url=e}let o=s.protocol==="wss:",f=s.protocol==="ws+unix:",u;if(s.protocol!=="ws:"&&!o&&!f?u=`The URL's protocol must be one of "ws:", "wss:", or "ws+unix:"`:f&&!s.pathname?u="The URL's pathname is empty":s.hash&&(u="The URL contains a fragment identifier"),u){let x=new SyntaxError(u);if(t._redirects===0)throw x;ko(t,x);return}let h=o?443:80,l=zg(16).toString("base64"),d=o?Vg.get:$g.get,m=new Set,_;if(n.createConnection=o?av:ov,n.defaultPort=n.defaultPort||h,n.port=s.port||h,n.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,n.headers={"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":l,Connection:"Upgrade",Upgrade:"websocket",...n.headers},n.path=s.pathname+s.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(_=new ei(n.perMessageDeflate!==!0?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=tv({[ei.extensionName]:_.offer()})),i.length){for(let x of i){if(typeof x!="string"||!nv.test(x)||m.has(x))throw new SyntaxError("An invalid or duplicated subprotocol was specified");m.add(x)}n.headers["Sec-WebSocket-Protocol"]=i.join(",")}if(n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(s.username||s.password)&&(n.auth=`${s.username}:${s.password}`),f){let x=n.path.split(":");n.socketPath=x[0],n.path=x[1]}let v=t._req=d(n);n.timeout&&v.on("timeout",()=>{xt(t,v,"Opening handshake has timed out")}),v.on("error",x=>{v===null||v.aborted||(v=t._req=null,ko(t,x))}),v.on("response",x=>{let b=x.headers.location,T=x.statusCode;if(b&&n.followRedirects&&T>=300&&T<400){if(++t._redirects>n.maxRedirects){xt(t,v,"Maximum redirects exceeded");return}v.abort();let B;try{B=new wo(b,e)}catch{let R=new SyntaxError(`Invalid URL: ${b}`);ko(t,R);return}Tf(t,B,i,r)}else t.emit("unexpected-response",v,x)||xt(t,v,`Unexpected server response: ${x.statusCode}`)}),v.on("upgrade",(x,b,T)=>{if(t.emit("upgrade",x),t.readyState!==Fe.CONNECTING)return;v=t._req=null;let B=Wg("sha1").update(l+Zg).digest("base64");if(x.headers["sec-websocket-accept"]!==B){xt(t,b,"Invalid Sec-WebSocket-Accept header");return}let E=x.headers["sec-websocket-protocol"],R;if(E!==void 0?m.size?m.has(E)||(R="Server sent an invalid subprotocol"):R="Server sent a subprotocol but none was requested":m.size&&(R="Server sent no subprotocol"),R){xt(t,b,R);return}E&&(t._protocol=E);let k=x.headers["sec-websocket-extensions"];if(k!==void 0){if(!_){xt(t,b,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let X;try{X=iv(k)}catch{xt(t,b,"Invalid Sec-WebSocket-Extensions header");return}let A=Object.keys(X);if(A.length!==1||A[0]!==ei.extensionName){xt(t,b,"Server indicated an extension that was not requested");return}try{_.accept(X[ei.extensionName])}catch{xt(t,b,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[ei.extensionName]=_}t.setSocket(b,T,{generateMask:n.generateMask,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation})})}function ko(t,e){t._readyState=Fe.CLOSING,t.emit("error",e),t.emitClose()}function ov(t){return t.path=t.socketPath,Cf.connect(t)}function av(t){return t.path=void 0,!t.servername&&t.servername!==""&&(t.servername=Cf.isIP(t.host)?"":t.host),Gg.connect(t)}function xt(t,e,i){t._readyState=Fe.CLOSING;let r=new Error(i);Error.captureStackTrace(r,xt),e.setHeader?(e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),e.once("abort",t.emitClose.bind(t)),t.emit("error",r)):(e.destroy(r),e.once("error",t.emit.bind(t,"error")),e.once("close",t.emitClose.bind(t)))}function Co(t,e,i){if(e){let r=rv(e).length;t._socket?t._sender._bufferedBytes+=r:t._bufferedAmount+=r}if(i){let r=new Error(`WebSocket is not open: readyState ${t.readyState} (${$t[t.readyState]})`);i(r)}}function lv(t,e){let i=this[Qe];i._closeFrameReceived=!0,i._closeMessage=e,i._closeCode=t,i._socket[Qe]!==void 0&&(i._socket.removeListener("data",gn),process.nextTick(If,i._socket),t===1005?i.close():i.close(t,e))}function cv(){let t=this[Qe];t.isPaused||t._socket.resume()}function uv(t){let e=this[Qe];e._socket[Qe]!==void 0&&(e._socket.removeListener("data",gn),process.nextTick(If,e._socket),e.close(t[Jg])),e.emit("error",t)}function kf(){this[Qe].emitClose()}function fv(t,e){this[Qe].emit("message",t,e)}function hv(t){let e=this[Qe];e.pong(t,!e._isServer,Of),e.emit("ping",t)}function pv(t){this[Qe].emit("pong",t)}function If(t){t.resume()}function Af(){let t=this[Qe];this.removeListener("close",Af),this.removeListener("data",gn),this.removeListener("end",Bf),t._readyState=Fe.CLOSING;let e;!this._readableState.endEmitted&&!t._closeFrameReceived&&!t._receiver._writableState.errorEmitted&&(e=t._socket.read())!==null&&t._receiver.write(e),t._receiver.end(),this[Qe]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",kf),t._receiver.on("finish",kf))}function gn(t){this[Qe]._receiver.write(t)||this.pause()}function Bf(){let t=this[Qe];t._readyState=Fe.CLOSING,t._receiver.end(),this.end()}function Rf(){let t=this[Qe];this.removeListener("error",Rf),this.on("error",Of),t&&(t._readyState=Fe.CLOSING,this.destroy())}});var Nf=S((Jx,Lf)=>{"use strict";var{tokenChars:dv}=_r();function mv(t){let e=new Set,i=-1,r=-1,n=0;for(n;n<t.length;n++){let o=t.charCodeAt(n);if(r===-1&&dv[o]===1)i===-1&&(i=n);else if(n!==0&&(o===32||o===9))r===-1&&i!==-1&&(r=n);else if(o===44){if(i===-1)throw new SyntaxError(`Unexpected character at index ${n}`);r===-1&&(r=n);let f=t.slice(i,r);if(e.has(f))throw new SyntaxError(`The "${f}" subprotocol is duplicated`);e.add(f),i=r=-1}else throw new SyntaxError(`Unexpected character at index ${n}`)}if(i===-1||r!==-1)throw new SyntaxError("Unexpected end of input");let s=t.slice(i,n);if(e.has(s))throw new SyntaxError(`The "${s}" subprotocol is duplicated`);return e.add(s),e}Lf.exports={parse:mv}});var qf=S((iy,jf)=>{"use strict";var gv=require("events"),vn=require("http"),Qx=require("https"),ey=require("net"),ty=require("tls"),{createHash:vv}=require("crypto"),Ff=bo(),wi=vr(),_v=Nf(),xv=Oo(),{GUID:yv,kWebSocket:bv}=Jt(),wv=/^[+/0-9A-Za-z]{22}==$/,Mf=0,Df=1,Uf=2,To=class extends gv{constructor(e,i){if(super(),e={maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=vn.createServer((r,n)=>{let s=vn.STATUS_CODES[426];n.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),n.end(s)}),this._server.listen(e.port,e.host,e.backlog,i)):e.server&&(this._server=e.server),this._server){let r=this.emit.bind(this,"connection");this._removeListeners=Sv(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(n,s,o)=>{this.handleUpgrade(n,s,o,r)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Mf}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===Uf){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(wr,this);return}if(e&&this.once("close",e),this._state!==Df)if(this._state=Df,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(wr,this):process.nextTick(wr,this);else{let i=this._server;this._removeListeners(),this._removeListeners=this._server=null,i.close(()=>{wr(this)})}}shouldHandle(e){if(this.options.path){let i=e.url.indexOf("?");if((i!==-1?e.url.slice(0,i):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,i,r,n){i.on("error",Io);let s=e.headers["sec-websocket-key"]!==void 0?e.headers["sec-websocket-key"]:!1,o=+e.headers["sec-websocket-version"];if(e.method!=="GET"||e.headers.upgrade.toLowerCase()!=="websocket"||!s||!wv.test(s)||o!==8&&o!==13||!this.shouldHandle(e))return Qi(i,400);let f=e.headers["sec-websocket-protocol"],u=new Set;if(f!==void 0)try{u=_v.parse(f)}catch{return Qi(i,400)}let h=e.headers["sec-websocket-extensions"],l={};if(this.options.perMessageDeflate&&h!==void 0){let d=new wi(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let m=Ff.parse(h);m[wi.extensionName]&&(d.accept(m[wi.extensionName]),l[wi.extensionName]=d)}catch{return Qi(i,400)}}if(this.options.verifyClient){let d={origin:e.headers[`${o===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(d,(m,_,v,x)=>{if(!m)return Qi(i,_||401,v,x);this.completeUpgrade(l,s,u,e,i,r,n)});return}if(!this.options.verifyClient(d))return Qi(i,401)}this.completeUpgrade(l,s,u,e,i,r,n)}completeUpgrade(e,i,r,n,s,o,f){if(!s.readable||!s.writable)return s.destroy();if(s[bv])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Mf)return Qi(s,503);let h=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${vv("sha1").update(i+yv).digest("base64")}`],l=new xv(null);if(r.size){let d=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;d&&(h.push(`Sec-WebSocket-Protocol: ${d}`),l._protocol=d)}if(e[wi.extensionName]){let d=e[wi.extensionName].params,m=Ff.format({[wi.extensionName]:[d]});h.push(`Sec-WebSocket-Extensions: ${m}`),l._extensions=e}this.emit("headers",h,n),s.write(h.concat(`\r
`).join(`\r
`)),s.removeListener("error",Io),l.setSocket(s,o,{maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(l),l.on("close",()=>{this.clients.delete(l),this._shouldEmitClose&&!this.clients.size&&process.nextTick(wr,this)})),f(l,n)}};jf.exports=To;function Sv(t,e){for(let i of Object.keys(e))t.on(i,e[i]);return function(){for(let r of Object.keys(e))t.removeListener(r,e[r])}}function wr(t){t._state=Uf,t.emit("close")}function Io(){this.destroy()}function Qi(t,e,i,r){t.writable&&(i=i||vn.STATUS_CODES[e],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(i),...r},t.write(`HTTP/1.1 ${e} ${vn.STATUS_CODES[e]}\r
`+Object.keys(r).map(n=>`${n}: ${r[n]}`).join(`\r
`)+`\r
\r
`+i)),t.removeListener("error",Io),t.destroy()}});var Gf=S((ny,$f)=>{var ti=require("constants"),kv=process.cwd,_n=null,Cv=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return _n||(_n=kv.call(process)),_n};try{process.cwd()}catch{}typeof process.chdir=="function"&&(Po=process.chdir,process.chdir=function(t){_n=null,Po.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,Po));var Po;$f.exports=Ov;function Ov(t){ti.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(t),t.lutimes||i(t),t.chown=s(t.chown),t.fchown=s(t.fchown),t.lchown=s(t.lchown),t.chmod=r(t.chmod),t.fchmod=r(t.fchmod),t.lchmod=r(t.lchmod),t.chownSync=o(t.chownSync),t.fchownSync=o(t.fchownSync),t.lchownSync=o(t.lchownSync),t.chmodSync=n(t.chmodSync),t.fchmodSync=n(t.fchmodSync),t.lchmodSync=n(t.lchmodSync),t.stat=f(t.stat),t.fstat=f(t.fstat),t.lstat=f(t.lstat),t.statSync=u(t.statSync),t.fstatSync=u(t.fstatSync),t.lstatSync=u(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(l,d,m){m&&process.nextTick(m)},t.lchmodSync=function(){}),t.chown&&!t.lchown&&(t.lchown=function(l,d,m,_){_&&process.nextTick(_)},t.lchownSync=function(){}),Cv==="win32"&&(t.rename=typeof t.rename!="function"?t.rename:function(l){function d(m,_,v){var x=Date.now(),b=0;l(m,_,function T(B){if(B&&(B.code==="EACCES"||B.code==="EPERM")&&Date.now()-x<6e4){setTimeout(function(){t.stat(_,function(E,R){E&&E.code==="ENOENT"?l(m,_,T):v(B)})},b),b<100&&(b+=10);return}v&&v(B)})}return Object.setPrototypeOf&&Object.setPrototypeOf(d,l),d}(t.rename)),t.read=typeof t.read!="function"?t.read:function(l){function d(m,_,v,x,b,T){var B;if(T&&typeof T=="function"){var E=0;B=function(R,k,X){if(R&&R.code==="EAGAIN"&&E<10)return E++,l.call(t,m,_,v,x,b,B);T.apply(this,arguments)}}return l.call(t,m,_,v,x,b,B)}return Object.setPrototypeOf&&Object.setPrototypeOf(d,l),d}(t.read),t.readSync=typeof t.readSync!="function"?t.readSync:function(l){return function(d,m,_,v,x){for(var b=0;;)try{return l.call(t,d,m,_,v,x)}catch(T){if(T.code==="EAGAIN"&&b<10){b++;continue}throw T}}}(t.readSync);function e(l){l.lchmod=function(d,m,_){l.open(d,ti.O_WRONLY|ti.O_SYMLINK,m,function(v,x){if(v){_&&_(v);return}l.fchmod(x,m,function(b){l.close(x,function(T){_&&_(b||T)})})})},l.lchmodSync=function(d,m){var _=l.openSync(d,ti.O_WRONLY|ti.O_SYMLINK,m),v=!0,x;try{x=l.fchmodSync(_,m),v=!1}finally{if(v)try{l.closeSync(_)}catch{}else l.closeSync(_)}return x}}function i(l){ti.hasOwnProperty("O_SYMLINK")&&l.futimes?(l.lutimes=function(d,m,_,v){l.open(d,ti.O_SYMLINK,function(x,b){if(x){v&&v(x);return}l.futimes(b,m,_,function(T){l.close(b,function(B){v&&v(T||B)})})})},l.lutimesSync=function(d,m,_){var v=l.openSync(d,ti.O_SYMLINK),x,b=!0;try{x=l.futimesSync(v,m,_),b=!1}finally{if(b)try{l.closeSync(v)}catch{}else l.closeSync(v)}return x}):l.futimes&&(l.lutimes=function(d,m,_,v){v&&process.nextTick(v)},l.lutimesSync=function(){})}function r(l){return l&&function(d,m,_){return l.call(t,d,m,function(v){h(v)&&(v=null),_&&_.apply(this,arguments)})}}function n(l){return l&&function(d,m){try{return l.call(t,d,m)}catch(_){if(!h(_))throw _}}}function s(l){return l&&function(d,m,_,v){return l.call(t,d,m,_,function(x){h(x)&&(x=null),v&&v.apply(this,arguments)})}}function o(l){return l&&function(d,m,_){try{return l.call(t,d,m,_)}catch(v){if(!h(v))throw v}}}function f(l){return l&&function(d,m,_){typeof m=="function"&&(_=m,m=null);function v(x,b){b&&(b.uid<0&&(b.uid+=4294967296),b.gid<0&&(b.gid+=4294967296)),_&&_.apply(this,arguments)}return m?l.call(t,d,m,v):l.call(t,d,v)}}function u(l){return l&&function(d,m){var _=m?l.call(t,d,m):l.call(t,d);return _&&(_.uid<0&&(_.uid+=4294967296),_.gid<0&&(_.gid+=4294967296)),_}}function h(l){if(!l||l.code==="ENOSYS")return!0;var d=!process.getuid||process.getuid()!==0;return!!(d&&(l.code==="EINVAL"||l.code==="EPERM"))}}});var Yf=S((sy,Wf)=>{var zf=require("stream").Stream;Wf.exports=Tv;function Tv(t){return{ReadStream:e,WriteStream:i};function e(r,n){if(!(this instanceof e))return new e(r,n);zf.call(this);var s=this;this.path=r,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,n=n||{};for(var o=Object.keys(n),f=0,u=o.length;f<u;f++){var h=o[f];this[h]=n[h]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){s._read()});return}t.open(this.path,this.flags,this.mode,function(l,d){if(l){s.emit("error",l),s.readable=!1;return}s.fd=d,s.emit("open",d),s._read()})}function i(r,n){if(!(this instanceof i))return new i(r,n);zf.call(this),this.path=r,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,n=n||{};for(var s=Object.keys(n),o=0,f=s.length;o<f;o++){var u=s[o];this[u]=n[u]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}});var Zf=S((oy,Kf)=>{"use strict";Kf.exports=Av;var Iv=Object.getPrototypeOf||function(t){return t.__proto__};function Av(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var e={__proto__:Iv(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(t,i))}),e}});var eh=S((ay,Fo)=>{var Le=require("fs"),Bv=Gf(),Rv=Yf(),Pv=Zf(),xn=require("util"),Ke,bn;typeof Symbol=="function"&&typeof Symbol.for=="function"?(Ke=Symbol.for("graceful-fs.queue"),bn=Symbol.for("graceful-fs.previous")):(Ke="___graceful-fs.queue",bn="___graceful-fs.previous");function Lv(){}function Qf(t,e){Object.defineProperty(t,Ke,{get:function(){return e}})}var Si=Lv;xn.debuglog?Si=xn.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(Si=function(){var t=xn.format.apply(xn,arguments);t="GFS4: "+t.split(/\n/).join(`
GFS4: `),console.error(t)});Le[Ke]||(Xf=global[Ke]||[],Qf(Le,Xf),Le.close=function(t){function e(i,r){return t.call(Le,i,function(n){n||Jf(),typeof r=="function"&&r.apply(this,arguments)})}return Object.defineProperty(e,bn,{value:t}),e}(Le.close),Le.closeSync=function(t){function e(i){t.apply(Le,arguments),Jf()}return Object.defineProperty(e,bn,{value:t}),e}(Le.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){Si(Le[Ke]),require("assert").equal(Le[Ke].length,0)}));var Xf;global[Ke]||Qf(global,Le[Ke]);Fo.exports=Lo(Pv(Le));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!Le.__patched&&(Fo.exports=Lo(Le),Le.__patched=!0);function Lo(t){Bv(t),t.gracefulify=Lo,t.createReadStream=k,t.createWriteStream=X;var e=t.readFile;t.readFile=i;function i(C,N,D){return typeof N=="function"&&(D=N,N=null),J(C,N,D);function J(j,se,M,$){return e(j,se,function(Y){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?er([J,[j,se,M],Y,$||Date.now(),Date.now()]):typeof M=="function"&&M.apply(this,arguments)})}}var r=t.writeFile;t.writeFile=n;function n(C,N,D,J){return typeof D=="function"&&(J=D,D=null),j(C,N,D,J);function j(se,M,$,Y,Q){return r(se,M,$,function(H){H&&(H.code==="EMFILE"||H.code==="ENFILE")?er([j,[se,M,$,Y],H,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var s=t.appendFile;s&&(t.appendFile=o);function o(C,N,D,J){return typeof D=="function"&&(J=D,D=null),j(C,N,D,J);function j(se,M,$,Y,Q){return s(se,M,$,function(H){H&&(H.code==="EMFILE"||H.code==="ENFILE")?er([j,[se,M,$,Y],H,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var f=t.copyFile;f&&(t.copyFile=u);function u(C,N,D,J){return typeof D=="function"&&(J=D,D=0),j(C,N,D,J);function j(se,M,$,Y,Q){return f(se,M,$,function(H){H&&(H.code==="EMFILE"||H.code==="ENFILE")?er([j,[se,M,$,Y],H,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var h=t.readdir;t.readdir=d;var l=/^v[0-5]\./;function d(C,N,D){typeof N=="function"&&(D=N,N=null);var J=l.test(process.version)?function(M,$,Y,Q){return h(M,j(M,$,Y,Q))}:function(M,$,Y,Q){return h(M,$,j(M,$,Y,Q))};return J(C,N,D);function j(se,M,$,Y){return function(Q,H){Q&&(Q.code==="EMFILE"||Q.code==="ENFILE")?er([J,[se,M,$],Q,Y||Date.now(),Date.now()]):(H&&H.sort&&H.sort(),typeof $=="function"&&$.call(this,Q,H))}}}if(process.version.substr(0,4)==="v0.8"){var m=Rv(t);T=m.ReadStream,E=m.WriteStream}var _=t.ReadStream;_&&(T.prototype=Object.create(_.prototype),T.prototype.open=B);var v=t.WriteStream;v&&(E.prototype=Object.create(v.prototype),E.prototype.open=R),Object.defineProperty(t,"ReadStream",{get:function(){return T},set:function(C){T=C},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return E},set:function(C){E=C},enumerable:!0,configurable:!0});var x=T;Object.defineProperty(t,"FileReadStream",{get:function(){return x},set:function(C){x=C},enumerable:!0,configurable:!0});var b=E;Object.defineProperty(t,"FileWriteStream",{get:function(){return b},set:function(C){b=C},enumerable:!0,configurable:!0});function T(C,N){return this instanceof T?(_.apply(this,arguments),this):T.apply(Object.create(T.prototype),arguments)}function B(){var C=this;z(C.path,C.flags,C.mode,function(N,D){N?(C.autoClose&&C.destroy(),C.emit("error",N)):(C.fd=D,C.emit("open",D),C.read())})}function E(C,N){return this instanceof E?(v.apply(this,arguments),this):E.apply(Object.create(E.prototype),arguments)}function R(){var C=this;z(C.path,C.flags,C.mode,function(N,D){N?(C.destroy(),C.emit("error",N)):(C.fd=D,C.emit("open",D))})}function k(C,N){return new t.ReadStream(C,N)}function X(C,N){return new t.WriteStream(C,N)}var A=t.open;t.open=z;function z(C,N,D,J){return typeof D=="function"&&(J=D,D=null),j(C,N,D,J);function j(se,M,$,Y,Q){return A(se,M,$,function(H,we){H&&(H.code==="EMFILE"||H.code==="ENFILE")?er([j,[se,M,$,Y],H,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}return t}function er(t){Si("ENQUEUE",t[0].name,t[1]),Le[Ke].push(t),No()}var yn;function Jf(){for(var t=Date.now(),e=0;e<Le[Ke].length;++e)Le[Ke][e].length>2&&(Le[Ke][e][3]=t,Le[Ke][e][4]=t);No()}function No(){if(clearTimeout(yn),yn=void 0,Le[Ke].length!==0){var t=Le[Ke].shift(),e=t[0],i=t[1],r=t[2],n=t[3],s=t[4];if(n===void 0)Si("RETRY",e.name,i),e.apply(null,i);else if(Date.now()-n>=6e4){Si("TIMEOUT",e.name,i);var o=i.pop();typeof o=="function"&&o.call(null,r)}else{var f=Date.now()-s,u=Math.max(s-n,1),h=Math.min(u*1.2,100);f>=h?(Si("RETRY",e.name,i),e.apply(null,i.concat([n]))):Le[Ke].push(t)}yn===void 0&&(yn=setTimeout(No,0))}}});var ih=S((ly,th)=>{function yt(t,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(t)),this._timeouts=t,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}th.exports=yt;yt.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};yt.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timeouts=[],this._cachedTimeouts=null};yt.prototype.retry=function(t){if(this._timeout&&clearTimeout(this._timeout),!t)return!1;var e=new Date().getTime();if(t&&e-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(t);var i=this._timeouts.shift();if(i===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),i=this._timeouts.shift();else return!1;var r=this,n=setTimeout(function(){r._attempts++,r._operationTimeoutCb&&(r._timeout=setTimeout(function(){r._operationTimeoutCb(r._attempts)},r._operationTimeout),r._options.unref&&r._timeout.unref()),r._fn(r._attempts)},i);return this._options.unref&&n.unref(),!0};yt.prototype.attempt=function(t,e){this._fn=t,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var i=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){i._operationTimeoutCb()},i._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};yt.prototype.try=function(t){console.log("Using RetryOperation.try() is deprecated"),this.attempt(t)};yt.prototype.start=function(t){console.log("Using RetryOperation.start() is deprecated"),this.attempt(t)};yt.prototype.start=yt.prototype.try;yt.prototype.errors=function(){return this._errors};yt.prototype.attempts=function(){return this._attempts};yt.prototype.mainError=function(){if(this._errors.length===0)return null;for(var t={},e=null,i=0,r=0;r<this._errors.length;r++){var n=this._errors[r],s=n.message,o=(t[s]||0)+1;t[s]=o,o>=i&&(e=n,i=o)}return e}});var rh=S(Ei=>{var Nv=ih();Ei.operation=function(t){var e=Ei.timeouts(t);return new Nv(e,{forever:t&&t.forever,unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})};Ei.timeouts=function(t){if(t instanceof Array)return[].concat(t);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var i in t)e[i]=t[i];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var r=[],n=0;n<e.retries;n++)r.push(this.createTimeout(n,e));return t&&t.forever&&!r.length&&r.push(this.createTimeout(n,e)),r.sort(function(s,o){return s-o}),r};Ei.createTimeout=function(t,e){var i=e.randomize?Math.random()+1:1,r=Math.round(i*e.minTimeout*Math.pow(e.factor,t));return r=Math.min(r,e.maxTimeout),r};Ei.wrap=function(t,e,i){if(e instanceof Array&&(i=e,e=null),!i){i=[];for(var r in t)typeof t[r]=="function"&&i.push(r)}for(var n=0;n<i.length;n++){var s=i[n],o=t[s];t[s]=function(u){var h=Ei.operation(e),l=Array.prototype.slice.call(arguments,1),d=l.pop();l.push(function(m){h.retry(m)||(m&&(arguments[0]=h.mainError()),d.apply(this,arguments))}),h.attempt(function(){u.apply(t,l)})}.bind(t,o),t[s].options=e}}});var sh=S((uy,nh)=>{nh.exports=rh()});var oh=S((fy,wn)=>{wn.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&wn.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&wn.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var fh=S((hy,rr)=>{var Pe=global.process,ki=function(t){return t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function"};ki(Pe)?(ah=require("assert"),tr=oh(),lh=/^win/i.test(Pe.platform),Sr=require("events"),typeof Sr!="function"&&(Sr=Sr.EventEmitter),Pe.__signal_exit_emitter__?ze=Pe.__signal_exit_emitter__:(ze=Pe.__signal_exit_emitter__=new Sr,ze.count=0,ze.emitted={}),ze.infinite||(ze.setMaxListeners(1/0),ze.infinite=!0),rr.exports=function(t,e){if(!ki(global.process))return function(){};ah.equal(typeof t,"function","a callback must be provided for exit handler"),ir===!1&&Mo();var i="exit";e&&e.alwaysLast&&(i="afterexit");var r=function(){ze.removeListener(i,t),ze.listeners("exit").length===0&&ze.listeners("afterexit").length===0&&Sn()};return ze.on(i,t),r},Sn=function(){!ir||!ki(global.process)||(ir=!1,tr.forEach(function(e){try{Pe.removeListener(e,En[e])}catch{}}),Pe.emit=kn,Pe.reallyExit=Do,ze.count-=1)},rr.exports.unload=Sn,Ci=function(e,i,r){ze.emitted[e]||(ze.emitted[e]=!0,ze.emit(e,i,r))},En={},tr.forEach(function(t){En[t]=function(){if(ki(global.process)){var i=Pe.listeners(t);i.length===ze.count&&(Sn(),Ci("exit",null,t),Ci("afterexit",null,t),lh&&t==="SIGHUP"&&(t="SIGINT"),Pe.kill(Pe.pid,t))}}}),rr.exports.signals=function(){return tr},ir=!1,Mo=function(){ir||!ki(global.process)||(ir=!0,ze.count+=1,tr=tr.filter(function(e){try{return Pe.on(e,En[e]),!0}catch{return!1}}),Pe.emit=uh,Pe.reallyExit=ch)},rr.exports.load=Mo,Do=Pe.reallyExit,ch=function(e){ki(global.process)&&(Pe.exitCode=e||0,Ci("exit",Pe.exitCode,null),Ci("afterexit",Pe.exitCode,null),Do.call(Pe,Pe.exitCode))},kn=Pe.emit,uh=function(e,i){if(e==="exit"&&ki(global.process)){i!==void 0&&(Pe.exitCode=i);var r=kn.apply(this,arguments);return Ci("exit",Pe.exitCode,null),Ci("afterexit",Pe.exitCode,null),r}else return kn.apply(this,arguments)}):rr.exports=function(){return function(){}};var ah,tr,lh,Sr,ze,Sn,Ci,En,ir,Mo,Do,ch,kn,uh});var xh=S((py,_h)=>{"use strict";var Fv=require("path"),mh=eh(),Mv=sh(),Dv=fh(),ii={},hh=Symbol();function Uv(t,e,i){let r=e[hh];if(r)return e.stat(t,(s,o)=>{if(s)return i(s);i(null,o.mtime,r)});let n=new Date(Math.ceil(Date.now()/1e3)*1e3+5);e.utimes(t,n,n,s=>{if(s)return i(s);e.stat(t,(o,f)=>{if(o)return i(o);let u=f.mtime.getTime()%1e3===0?"s":"ms";Object.defineProperty(e,hh,{value:u}),i(null,f.mtime,u)})})}function jv(t){let e=Date.now();return t==="s"&&(e=Math.ceil(e/1e3)*1e3),new Date(e)}function On(t,e){return e.lockfilePath||`${t}.lock`}function gh(t,e,i){if(!e.realpath)return i(null,Fv.resolve(t));e.fs.realpath(t,i)}function jo(t,e,i){let r=On(t,e);e.fs.mkdir(r,n=>{if(!n)return Uv(r,e.fs,(s,o,f)=>{if(s)return e.fs.rmdir(r,()=>{}),i(s);i(null,o,f)});if(n.code!=="EEXIST")return i(n);if(e.stale<=0)return i(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));e.fs.stat(r,(s,o)=>{if(s)return s.code==="ENOENT"?jo(t,{...e,stale:0},i):i(s);if(!qv(o,e))return i(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));vh(t,e,f=>{if(f)return i(f);jo(t,{...e,stale:0},i)})})})}function qv(t,e){return t.mtime.getTime()<Date.now()-e.stale}function vh(t,e,i){e.fs.rmdir(On(t,e),r=>{if(r&&r.code!=="ENOENT")return i(r);i()})}function Cn(t,e){let i=ii[t];i.updateTimeout||(i.updateDelay=i.updateDelay||e.update,i.updateTimeout=setTimeout(()=>{i.updateTimeout=null,e.fs.stat(i.lockfilePath,(r,n)=>{let s=i.lastUpdate+e.stale<Date.now();if(r)return r.code==="ENOENT"||s?Uo(t,i,Object.assign(r,{code:"ECOMPROMISED"})):(i.updateDelay=1e3,Cn(t,e));if(!(i.mtime.getTime()===n.mtime.getTime()))return Uo(t,i,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let f=jv(i.mtimePrecision);e.fs.utimes(i.lockfilePath,f,f,u=>{let h=i.lastUpdate+e.stale<Date.now();if(!i.released){if(u)return u.code==="ENOENT"||h?Uo(t,i,Object.assign(u,{code:"ECOMPROMISED"})):(i.updateDelay=1e3,Cn(t,e));i.mtime=f,i.lastUpdate=Date.now(),i.updateDelay=null,Cn(t,e)}})})},i.updateDelay),i.updateTimeout.unref&&i.updateTimeout.unref())}function Uo(t,e,i){e.released=!0,e.updateTimeout&&clearTimeout(e.updateTimeout),ii[t]===e&&delete ii[t],e.options.onCompromised(i)}function Hv(t,e,i){e={stale:1e4,update:null,realpath:!0,retries:0,fs:mh,onCompromised:r=>{throw r},...e},e.retries=e.retries||0,e.retries=typeof e.retries=="number"?{retries:e.retries}:e.retries,e.stale=Math.max(e.stale||0,2e3),e.update=e.update==null?e.stale/2:e.update||0,e.update=Math.max(Math.min(e.update,e.stale/2),1e3),gh(t,e,(r,n)=>{if(r)return i(r);let s=Mv.operation(e.retries);s.attempt(()=>{jo(n,e,(o,f,u)=>{if(s.retry(o))return;if(o)return i(s.mainError());let h=ii[n]={lockfilePath:On(n,e),mtime:f,mtimePrecision:u,options:e,lastUpdate:Date.now()};Cn(n,e),i(null,l=>{if(h.released)return l&&l(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));Vv(n,{...e,realpath:!1},l)})})})})}function Vv(t,e,i){e={fs:mh,realpath:!0,...e},gh(t,e,(r,n)=>{if(r)return i(r);let s=ii[n];if(!s)return i(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));s.updateTimeout&&clearTimeout(s.updateTimeout),s.released=!0,delete ii[n],vh(n,e,i)})}function ph(t){return(...e)=>new Promise((i,r)=>{e.push((n,s)=>{n?r(n):i(s)}),t(...e)})}var dh=!1;function $v(){dh||(dh=!0,Dv(()=>{for(let t in ii){let e=ii[t].options;try{e.fs.rmdirSync(On(t,e))}catch{}}}))}_h.exports.lock=async(t,e)=>{$v();let i=await ph(Hv)(t,e);return ph(i)}});var s_={};Qh(s_,{HttpsProxyAgent:()=>Ih.HttpsProxyAgent,PNG:()=>Ah.PNG,SocksProxyAgent:()=>Rh.SocksProxyAgent,StackUtils:()=>e_,colors:()=>Gv,debug:()=>zv,getProxyForUrl:()=>Th.getProxyForUrl,jpegjs:()=>Wv,lockfile:()=>Kv,mime:()=>Zv,minimatch:()=>Xv,open:()=>Jv,program:()=>Bh.program,progress:()=>Qv,ws:()=>t_,wsReceiver:()=>r_,wsSender:()=>n_,wsServer:()=>i_});module.exports=ep(s_);var yh=He(_a()),bh=He(Ni()),Th=He(Aa()),Ih=He(Ua()),wh=He(za()),Sh=He(el()),Eh=He(bl()),kh=He(Nl()),Ah=He(Ac()),Bh=He(Vc()),Ch=He(Yc()),Rh=He(Au()),Oh=He(Fu());var Ev=He(ju(),1),Ao=He(vo(),1),Bo=He(xo(),1),Hf=He(Oo(),1),Ro=He(qf(),1);var Vf=Hf.default;var Gv=yh.default,zv=bh.default,Wv=wh.default,Yv=xh(),Kv=Yv,Zv=Sh.default,Xv=Eh.default,Jv=kh.default,Qv=Ch.default,e_=Oh.default,t_=Vf,i_=Ro.default,r_=Ao.default,n_=Bo.default;0&&(module.exports={HttpsProxyAgent,PNG,SocksProxyAgent,StackUtils,colors,debug,getProxyForUrl,jpegjs,lockfile,mime,minimatch,open,program,progress,ws,wsReceiver,wsSender,wsServer});
/*! Bundled license information:

progress/lib/node-progress.js:
  (*!
   * node-progress
   * Copyright(c) 2011 TJ Holowaychuk <<EMAIL>>
   * MIT Licensed
   *)
*/
