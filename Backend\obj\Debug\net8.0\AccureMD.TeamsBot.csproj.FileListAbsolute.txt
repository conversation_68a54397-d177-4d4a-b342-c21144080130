D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\appsettings.Development.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\appsettings.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\TeamsAppManifest\manifest.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\test-auth.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\test-database.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.staticwebassets.runtime.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.staticwebassets.endpoints.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.exe
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.deps.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.runtimeconfig.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AccureMD.TeamsBot.pdb
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\AdaptiveCards.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Azure.Core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Azure.Identity.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Azure.Storage.Blobs.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Azure.Storage.Common.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Humanizer.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bcl.Memory.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Builder.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Builder.Integration.AspNet.Core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Configuration.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Connector.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Connector.Streaming.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Schema.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Bot.Streaming.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Design.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Graph.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Graph.Core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Identity.Client.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.IdentityModel.Validators.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Abstractions.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Authentication.Azure.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Http.HttpClientLibrary.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Form.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Json.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Multipart.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Text.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Rest.ClientRuntime.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Mono.TextTemplating.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Newtonsoft.Json.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Npgsql.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Std.UriTemplate.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.ClientModel.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.CodeDom.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Composition.AttributedModel.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Composition.Convention.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Composition.Hosting.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Composition.Runtime.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Composition.TypedParts.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.IO.Hashing.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Memory.Data.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.csproj.AssemblyReference.cache
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\rpswa.dswa.cache.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.GeneratedMSBuildEditorConfig.editorconfig
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.AssemblyInfoInputs.cache
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.AssemblyInfo.cs
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.csproj.CoreCompileInputs.cache
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.MvcApplicationPartsAssemblyInfo.cs
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.MvcApplicationPartsAssemblyInfo.cache
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\rjimswa.dswa.cache.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\scopedcss\bundle\AccureMD.TeamsBot.styles.css
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\staticwebassets.build.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\staticwebassets.build.json.cache
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\staticwebassets.development.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\staticwebassets.build.endpoints.json
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.653F3958.Up2Date
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.dll
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\refint\AccureMD.TeamsBot.dll
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.pdb
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\AccureMD.TeamsBot.genruntimeconfig.cache
D:\iData Project\ASR_Bot_New\Backend\obj\Debug\net8.0\ref\AccureMD.TeamsBot.dll
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\TeamsAppManifest\new4\manifest.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\node\win32_x64\node.exe
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\node\LICENSE
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\api.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\install_media_pack.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\PrintDeps.exe
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\README.md
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_beta_linux.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_beta_mac.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_beta_win.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_stable_linux.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_stable_mac.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_stable_win.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_beta_linux.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_beta_mac.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_beta_win.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_dev_linux.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_dev_mac.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_dev_win.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_stable_linux.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_stable_mac.sh
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_stable_win.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\browsers.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\cli.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\index.d.ts
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\index.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\index.mjs
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\androidServerImpl.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\browserServerImpl.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\accessibility.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\android.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\api.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\artifact.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\browser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\browserContext.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\browserType.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\cdpSession.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\channelOwner.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\clientHelper.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\clientInstrumentation.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\clock.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\connection.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\consoleMessage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\coverage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\dialog.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\download.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\electron.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\elementHandle.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\errors.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\events.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\fetch.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\fileChooser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\frame.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\harRouter.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\input.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\jsHandle.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\jsonPipe.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\localUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\locator.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\network.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\page.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\playwright.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\selectors.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\stream.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\tracing.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\types.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\video.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\waiter.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\webError.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\worker.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\client\writableStream.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\cli\driver.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\cli\program.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\cli\programWithTestStub.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\common\socksProxy.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\common\timeoutSettings.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\common\types.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\generated\clockSource.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\generated\consoleApiSource.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\generated\injectedScriptSource.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\generated\recorderSource.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\generated\utilityScriptSource.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\image_tools\colorUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\image_tools\compare.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\image_tools\imageChannel.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\image_tools\stats.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\inprocess.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\inProcessFactory.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\outofprocess.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\protocol\debug.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\protocol\serializers.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\protocol\transport.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\protocol\validator.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\protocol\validatorPrimitives.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\remote\playwrightConnection.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\remote\playwrightServer.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\accessibility.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\android\android.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\android\backendAdb.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\artifact.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\browser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\browserContext.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\browserType.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\appIcon.png
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\chromium.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\chromiumSwitches.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crAccessibility.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crBrowser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crConnection.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crCoverage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crDevTools.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crDragDrop.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crExecutionContext.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crInput.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crNetworkManager.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crPage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crPdf.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crProtocolHelper.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crServiceWorker.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\defaultFontFamilies.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\chromium\videoRecorder.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\clock.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\console.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\cookieStore.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\debugController.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\debugger.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\deviceDescriptors.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\deviceDescriptorsSource.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dialog.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\androidDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\artifactDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\browserContextDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\browserDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\browserTypeDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\cdpSessionDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\debugControllerDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\dialogDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\dispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\electronDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\elementHandlerDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\frameDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\jsHandleDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\jsonPipeDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\localUtilsDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\networkDispatchers.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\pageDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\playwrightDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\selectorsDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\streamDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\tracingDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\writableStreamDispatcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\dom.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\download.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\electron\electron.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\electron\loader.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\errors.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\fetch.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\fileChooser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\fileUploadUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffAccessibility.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffBrowser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffConnection.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffExecutionContext.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffInput.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffNetworkManager.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffPage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\firefox\firefox.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\formData.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\frames.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\frameSelectors.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\har\harRecorder.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\har\harTracer.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\helper.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\index.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\input.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\instrumentation.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\isomorphic\utilityScriptSerializers.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\javascript.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\launchApp.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\macEditingCommands.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\network.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\page.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\pipeTransport.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\playwright.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\progress.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\protocolError.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\codeGenerator.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\csharp.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\java.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\javascript.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\jsonl.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\language.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\python.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderActions.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderApp.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\recorder\utils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\registry\browserFetcher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\registry\dependencies.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\registry\index.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\registry\nativeDeps.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\registry\oopDownloadBrowserMain.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\screenshotter.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\selectors.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\socksInterceptor.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\trace\recorder\snapshotter.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\trace\recorder\snapshotterInjected.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\trace\recorder\tracing.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\trace\test\inMemorySnapshotter.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\trace\viewer\traceViewer.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\transport.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\types.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\usKeyboardLayout.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\webkit.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkAccessibility.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkBrowser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkConnection.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkExecutionContext.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkInput.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkInterceptableRequest.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkPage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkProvisionalPage.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkWorkers.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\third_party\diff_match_patch.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\third_party\pixelmatch.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utilsBundle.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utilsBundleImpl\index.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utilsBundleImpl\xdg-open
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\ascii.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\comparators.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\crypto.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\debug.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\debugLogger.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\env.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\eventsHelper.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\fileUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\glob.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\happy-eyeballs.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\headers.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\hostPlatform.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\httpServer.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\index.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\cssParser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\cssTokenizer.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\locatorGenerators.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\locatorParser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\locatorUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\selectorParser.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\stringUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\traceUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\linuxUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\manualPromise.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\mimeType.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\multimap.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\network.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\processLauncher.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\profiler.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\rtti.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\semaphore.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\spawnAsync.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\stackTrace.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\task.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\time.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\timeoutRunner.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\traceUtils.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\userAgent.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\wsServer.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\zipFile.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\utils\zones.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\htmlReport\index.html
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\codeMirrorModule-Bzggq412.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\codeMirrorModule-ez37Vkbh.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\codicon-DMa5iZz2.ttf
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\index-5SSs4-n_.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\index-B5iDPo7P.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\index.html
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\playwright-logo.svg
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\assets\codeMirrorModule-BfR9u_dG.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\assets\testServerConnection-CLJOcN3M.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\assets\xtermModule-BeNbaIVa.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\codeMirrorModule.ez37Vkbh.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\codicon.DMa5iZz2.ttf
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\index.CrbWWHbf.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\index.D7JIa5vL.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\index.html
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\playwright-logo.svg
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\snapshot.html
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\sw.bundle.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\testServerConnection.0WapKERv.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\uiMode.Btn2TM9w.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\uiMode.DGx1ltIi.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\uiMode.html
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\xtermModule.DSXBckUd.css
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\zipBundle.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\lib\zipBundleImpl.js
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\LICENSE
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\NOTICE
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\package.json
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\protocol.yml
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\README.md
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\ThirdPartyNotices.txt
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\types\protocol.d.ts
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\types\structs.d.ts
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\.playwright\package\types\types.d.ts
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\playwright.ps1
D:\iData Project\ASR_Bot_New\Backend\bin\Debug\net8.0\Microsoft.Playwright.dll
